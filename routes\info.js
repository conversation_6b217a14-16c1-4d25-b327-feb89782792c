const express = require('express')
const infoController = require('../controllers/infoController')
const router = express.Router()

router.get('/list', function (req, res) {
	let { title, pageIndex, pageSize, source, cropId } = req.query
	pageIndex = pageIndex || 0
	pageSize = pageSize || 10
	infoController.getInfoList(pageIndex, pageSize, {
		title,
		source,
		cropId
	}).then(data => {
		res.sendSuccess(data)
	}).catch(err => {
		res.sendMessage(err)
	})
})

router.get('/detail/:id', function (req, res) {
	let { id } = req.params
	infoController.getInfoById(id).then(data => {
		res.sendSuccess(data)
	}).catch(err => {
		res.sendMessage(err)
	})
})

module.exports = router
