<template>
    <div class="card">
        <div class="search_wrapper">
            <el-date-picker v-model="searchParams.date" type="date" value-format="yyyy-MM-dd" placeholder="选择日期"></el-date-picker>
            <el-button type="primary" @click="loadData">查询</el-button>
            <el-button @click="clear">清空</el-button>
            <el-button style="width: 140px" type="primary" @click="showAdd">新增港口信息</el-button>
        </div>
        <div class="card-body">
            <el-table v-loading="loading" :data="tableData" border fit highlight-current-row style="width:100%">
                <el-table-column label="ID" prop="id" align="center" width="50"></el-table-column>
                <el-table-column label="鲅鱼圈" prop="port1" align="center">
                    <template slot-scope="{ row }">
                        <el-tag type="danger" v-if="row.port1 > 0">上涨</el-tag>
                        <el-tag type="success" v-else-if="row.port1 < 0">下跌</el-tag>
                        <el-tag type="info" v-else>平</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="锦州港" prop="port2" align="center">
                    <template slot-scope="{ row }">
                        <el-tag type="danger" v-if="row.port2 > 0">上涨</el-tag>
                        <el-tag type="success" v-else-if="row.port2 < 0">下跌</el-tag>
                        <el-tag type="info" v-else>平</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="玉米厂车" prop="carCount" align="center"></el-table-column>
                <el-table-column label="厂车较昨日" prop="carMinusYesterday" align="center"></el-table-column>
                <el-table-column label="报价日期" prop="date" align="center"></el-table-column>
                <el-table-column label="操作" align="center">
                    <template slot-scope="{ row }">
                        <el-button size="mini" @click="showEdit(row)">编辑</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <Pagination v-if="total > 0" :total="total" :pageIndex.sync="pageIndex" :pageSize.sync="pageSize" @pagination="loadData" />
        </div>
        <el-dialog :title="editItem.id ? '编辑港口信息' : '新增港口信息'" :visible.sync="editDialogVisible">
            <el-form label-width="100px">
                <el-form-item label="鲅鱼圈">
                    <el-radio-group v-model="editItem.port1">
                        <el-radio-button :label="1">上涨</el-radio-button>
                        <el-radio-button :label="-1">下跌</el-radio-button>
                        <el-radio-button :label="0">持平</el-radio-button>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="锦州港">
                    <el-radio-group v-model="editItem.port2">
                        <el-radio-button :label="1">上涨</el-radio-button>
                        <el-radio-button :label="-1">下跌</el-radio-button>
                        <el-radio-button :label="0">持平</el-radio-button>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="玉米厂车">
                    <el-input v-model="editItem.carCount" placeholder="请输入今日玉米厂车数量"></el-input>
                </el-form-item>
                <el-form-item label="选择日期">
                    <el-date-picker v-model="editItem.date" type="date" value-format="yyyy-MM-dd" placeholder="选择日期"></el-date-picker>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="editDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="onEditBtnConfirm">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import cropOtherManager from '@/manager/cropOtherManager'
import Pagination from '@/components/Pagination'
import tableMixin from '@/mixins/tableMixin'
export default {
    name: 'CropOtherDaily',
    data () {
        return {
            searchParams: {
                date: null
            },
            editDialogVisible: false,
            editItem: {
                id: null,
                port1: 0,
                port2: 0,
                carCount: null,
                date: new Date().format('yyyy-MM-dd')
            }
        }
    },
    components: { Pagination },
    mixins: [tableMixin],
    methods: {
        loadData () {
            const { searchParams: { date }, pageIndex, pageSize } = this;
            this.loading = true;
            cropOtherManager.queryOthers(date, pageIndex, pageSize).then(res => {
                const { total, list } = res;
                this.total = total;
                this.tableData = list.map(item => {
                    item.date = new Date(item.date).format('yyyy-MM-dd');
                    return item;
                });
            }).catch(err => {
                this.$message.error(err)
            }).then(() => {
                this.loading = false;
            })
        },
        showEdit (item) {
            Object.assign(this.editItem, item);
            this.editDialogVisible = true;
        },
        showAdd () {
            this.editItem = {
                port1: 0,
                port2: 0,
                carCount: null,
                date: new Date().format('yyyy-MM-dd')
            };
            this.editDialogVisible = true;
        },
        onEditBtnConfirm () {
            const { id, port1, port2, carCount, date } = this.editItem;
            if (id) {
                cropOtherManager.updateOther(id, port1, port2, carCount, date).then(() => {
                    this.$message.success('更新成功');
                    this.editDialogVisible = false;
                    this.loadData();
                }).catch(err => {
                    this.$message.error(err)
                })
            } else {
                cropOtherManager.addOther(port1, port2, carCount, date).then(() => {
                    this.$message.success('添加成功');
                    this.editDialogVisible = false;
                    this.loadData();
                }).catch(err => {
                    this.$message.error(err)
                });
            }
        },
        clear () {
            this.searchParams.date = null;
        }
    }
}
</script>