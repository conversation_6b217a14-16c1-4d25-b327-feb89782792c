<template>
  <div :class="{'hidden':hidden}" class="pagination-container">
    <el-pagination
      :background="background"
      :current-page.sync="elPageIndex"
      :page-size.sync="elPageSize"
      :layout="layout"
      :page-sizes="pageSizes"
      :total="total"
      v-bind="$attrs"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script>
import { scrollTo } from '@/utils/scroll-to'

export default {
  name: 'Pagination',
  props: {
    total: {
      required: true,
      type: Number
    },
    pageIndex: {
      type: Number,
      default: 0
    },
    pageSize: {
      type: Number,
      default: 20
    },
    pageSizes: {
      type: Array,
      default(){
        return [10, 20, 30, 50]
      }
    },
    layout: {
      type: String,
      default: 'total, sizes, prev, pager, next, jumper'
    },
    background: {
      type: Boolean,
      default: true
    },
    autoScroll: {
      type: Boolean,
      default: true
    },
    hidden: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    }
  },
  computed: {
    elPageIndex: {
      get() {
        return this.pageIndex + 1
      },
      set(val) {
        this.$emit('update:pageIndex', val - 1)
      }
    },
    elPageSize: {
      get() {
        return this.pageSize
      },
      set(val) {
        this.$emit('update:pageSize', val)
      }
    }
  },
  methods: {
    handleSizeChange(val) {
      this.$emit('pagination', { pageIndex: this.elPageIndex - 1, pageSize: val })
      if (this.autoScroll) scrollTo(0, 800)
    },
    handleCurrentChange(val) {
      this.$emit('pagination', { pageIndex: val - 1, pageSize: this.elPageSize })
      if (this.autoScroll) scrollTo(0, 800)
    }
  }
}
</script>

<style scoped>
.pagination-container {
  background: #fff;
  margin-top: 20px;
}
.pagination-container.hidden {
  display: none;
}
</style>
