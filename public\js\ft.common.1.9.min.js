/* Include Zepto、ZeptoTouchEvent、ftUtils...  20181127 By <PERSON><PERSON><PERSON> */
var Zepto=function(){function L(t){return null==t?String(t):j[S.call(t)]||"object"}function Z(t){return"function"==L(t)}function $(t){return null!=t&&t==t.window}function _(t){return null!=t&&t.nodeType==t.DOCUMENT_NODE}function D(t){return"object"==L(t)}function R(t){return D(t)&&!$(t)&&Object.getPrototypeOf(t)==Object.prototype}function M(t){return"number"==typeof t.length}function k(t){return s.call(t,function(t){return null!=t})}function z(t){return t.length>0?n.fn.concat.apply([],t):t}function F(t){return t.replace(/::/g,"/").replace(/([A-Z]+)([A-Z][a-z])/g,"$1_$2").replace(/([a-z\d])([A-Z])/g,"$1_$2").replace(/_/g,"-").toLowerCase()}function q(t){return t in f?f[t]:f[t]=new RegExp("(^|\\s)"+t+"(\\s|$)")}function H(t,e){return"number"!=typeof e||c[F(t)]?e:e+"px"}function I(t){var e,n;return u[t]||(e=a.createElement(t),a.body.appendChild(e),n=getComputedStyle(e,"").getPropertyValue("display"),e.parentNode.removeChild(e),"none"==n&&(n="block"),u[t]=n),u[t]}function V(t){return"children" in t?o.call(t.children):n.map(t.childNodes,function(t){return 1==t.nodeType?t:void 0})}function B(n,i,r){for(e in i){r&&(R(i[e])||A(i[e]))?(R(i[e])&&!R(n[e])&&(n[e]={}),A(i[e])&&!A(n[e])&&(n[e]=[]),B(n[e],i[e],r)):i[e]!==t&&(n[e]=i[e])}}function U(t,e){return null==e?n(t):n(t).filter(e)}function J(t,e,n,i){return Z(e)?e.call(t,n,i):e}function X(t,e,n){null==n?t.removeAttribute(e):t.setAttribute(e,n)}function W(e,n){var i=e.className,r=i&&i.baseVal!==t;return n===t?r?i.baseVal:i:void (r?i.baseVal=n:e.className=n)}function Y(t){var e;try{return t?"true"==t||("false"==t?!1:"null"==t?null:/^0/.test(t)||isNaN(e=Number(t))?/^[\[\{]/.test(t)?n.parseJSON(t):t:e):t}catch(i){return t}}function G(t,e){e(t);for(var n=0,i=t.childNodes.length;i>n;n++){G(t.childNodes[n],e)}}var t,e,n,i,C,N,r=[],o=r.slice,s=r.filter,a=window.document,u={},f={},c={"column-count":1,columns:1,"font-weight":1,"line-height":1,opacity:1,"z-index":1,zoom:1},l=/^\s*<(\w+|!)[^>]*>/,h=/^<(\w+)\s*\/?>(?:<\/\1>|)$/,p=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:]+)[^>]*)\/>/gi,d=/^(?:body|html)$/i,m=/([A-Z])/g,g=["val","css","html","text","data","width","height","offset"],v=["after","prepend","before","append"],y=a.createElement("table"),x=a.createElement("tr"),b={tr:a.createElement("tbody"),tbody:y,thead:y,tfoot:y,td:x,th:x,"*":a.createElement("div")},w=/complete|loaded|interactive/,E=/^[\w-]*$/,j={},S=j.toString,T={},O=a.createElement("div"),P={tabindex:"tabIndex",readonly:"readOnly","for":"htmlFor","class":"className",maxlength:"maxLength",cellspacing:"cellSpacing",cellpadding:"cellPadding",rowspan:"rowSpan",colspan:"colSpan",usemap:"useMap",frameborder:"frameBorder",contenteditable:"contentEditable"},A=Array.isArray||function(t){return t instanceof Array};return T.matches=function(t,e){if(!e||!t||1!==t.nodeType){return !1}var n=t.webkitMatchesSelector||t.mozMatchesSelector||t.oMatchesSelector||t.matchesSelector;if(n){return n.call(t,e)}var i,r=t.parentNode,o=!r;return o&&(r=O).appendChild(t),i=~T.qsa(r,e).indexOf(t),o&&O.removeChild(t),i},C=function(t){return t.replace(/-+(.)?/g,function(t,e){return e?e.toUpperCase():""})},N=function(t){return s.call(t,function(e,n){return t.indexOf(e)==n})},T.fragment=function(e,i,r){var s,u,f;return h.test(e)&&(s=n(a.createElement(RegExp.$1))),s||(e.replace&&(e=e.replace(p,"<$1></$2>")),i===t&&(i=l.test(e)&&RegExp.$1),i in b||(i="*"),f=b[i],f.innerHTML=""+e,s=n.each(o.call(f.childNodes),function(){f.removeChild(this)})),R(r)&&(u=n(s),n.each(r,function(t,e){g.indexOf(t)>-1?u[t](e):u.attr(t,e)})),s},T.Z=function(t,e){return t=t||[],t.__proto__=n.fn,t.selector=e||"",t},T.isZ=function(t){return t instanceof T.Z},T.init=function(e,i){var r;if(!e){return T.Z()}if("string"==typeof e){if(e=e.trim(),"<"==e[0]&&l.test(e)){r=T.fragment(e,RegExp.$1,i),e=null}else{if(i!==t){return n(i).find(e)}r=T.qsa(a,e)}}else{if(Z(e)){return n(a).ready(e)}if(T.isZ(e)){return e}if(A(e)){r=k(e)}else{if(D(e)){r=[e],e=null}else{if(l.test(e)){r=T.fragment(e.trim(),RegExp.$1,i),e=null}else{if(i!==t){return n(i).find(e)}r=T.qsa(a,e)}}}}return T.Z(r,e)},n=function(t,e){return T.init(t,e)},n.extend=function(t){var e,n=o.call(arguments,1);return"boolean"==typeof t&&(e=t,t=n.shift()),n.forEach(function(n){B(t,n,e)}),t},T.qsa=function(t,e){var n,i="#"==e[0],r=!i&&"."==e[0],s=i||r?e.slice(1):e,a=E.test(s);return _(t)&&a&&i?(n=t.getElementById(s))?[n]:[]:1!==t.nodeType&&9!==t.nodeType?[]:o.call(a&&!i?r?t.getElementsByClassName(s):t.getElementsByTagName(e):t.querySelectorAll(e))},n.contains=a.documentElement.contains?function(t,e){return t!==e&&t.contains(e)}:function(t,e){for(;e&&(e=e.parentNode);){if(e===t){return !0}}return !1},n.type=L,n.isFunction=Z,n.isWindow=$,n.isArray=A,n.isPlainObject=R,n.isEmptyObject=function(t){var e;for(e in t){return !1}return !0},n.inArray=function(t,e,n){return r.indexOf.call(e,t,n)},n.camelCase=C,n.trim=function(t){return null==t?"":String.prototype.trim.call(t)},n.uuid=0,n.support={},n.expr={},n.map=function(t,e){var n,r,o,i=[];if(M(t)){for(r=0;r<t.length;r++){n=e(t[r],r),null!=n&&i.push(n)}}else{for(o in t){n=e(t[o],o),null!=n&&i.push(n)}}return z(i)},n.each=function(t,e){var n,i;if(M(t)){for(n=0;n<t.length;n++){if(e.call(t[n],n,t[n])===!1){return t}}}else{for(i in t){if(e.call(t[i],i,t[i])===!1){return t}}}return t},n.grep=function(t,e){return s.call(t,e)},window.JSON&&(n.parseJSON=JSON.parse),n.each("Boolean Number String Function Array Date RegExp Object Error".split(" "),function(t,e){j["[object "+e+"]"]=e.toLowerCase()}),n.fn={forEach:r.forEach,reduce:r.reduce,push:r.push,sort:r.sort,indexOf:r.indexOf,concat:r.concat,map:function(t){return n(n.map(this,function(e,n){return t.call(e,n,e)}))},slice:function(){return n(o.apply(this,arguments))},ready:function(t){return w.test(a.readyState)&&a.body?t(n):a.addEventListener("DOMContentLoaded",function(){t(n)},!1),this},get:function(e){return e===t?o.call(this):this[e>=0?e:e+this.length]},toArray:function(){return this.get()},size:function(){return this.length},remove:function(){return this.each(function(){null!=this.parentNode&&this.parentNode.removeChild(this)})},each:function(t){return r.every.call(this,function(e,n){return t.call(e,n,e)!==!1}),this},filter:function(t){return Z(t)?this.not(this.not(t)):n(s.call(this,function(e){return T.matches(e,t)}))},add:function(t,e){return n(N(this.concat(n(t,e))))},is:function(t){return this.length>0&&T.matches(this[0],t)},not:function(e){var i=[];if(Z(e)&&e.call!==t){this.each(function(t){e.call(this,t)||i.push(this)})}else{var r="string"==typeof e?this.filter(e):M(e)&&Z(e.item)?o.call(e):n(e);this.forEach(function(t){r.indexOf(t)<0&&i.push(t)})}return n(i)},has:function(t){return this.filter(function(){return D(t)?n.contains(this,t):n(this).find(t).size()})},eq:function(t){return -1===t?this.slice(t):this.slice(t,+t+1)},first:function(){var t=this[0];return t&&!D(t)?t:n(t)},last:function(){var t=this[this.length-1];return t&&!D(t)?t:n(t)},find:function(t){var e,i=this;return e=t?"object"==typeof t?n(t).filter(function(){var t=this;return r.some.call(i,function(e){return n.contains(e,t)})}):1==this.length?n(T.qsa(this[0],t)):this.map(function(){return T.qsa(this,t)}):[]},closest:function(t,e){var i=this[0],r=!1;for("object"==typeof t&&(r=n(t));i&&!(r?r.indexOf(i)>=0:T.matches(i,t));){i=i!==e&&!_(i)&&i.parentNode}return n(i)},parents:function(t){for(var e=[],i=this;i.length>0;){i=n.map(i,function(t){return(t=t.parentNode)&&!_(t)&&e.indexOf(t)<0?(e.push(t),t):void 0})}return U(e,t)},parent:function(t){return U(N(this.pluck("parentNode")),t)},children:function(t){return U(this.map(function(){return V(this)}),t)},contents:function(){return this.map(function(){return o.call(this.childNodes)})},siblings:function(t){return U(this.map(function(t,e){return s.call(V(e.parentNode),function(t){return t!==e})}),t)},empty:function(){return this.each(function(){this.innerHTML=""})},pluck:function(t){return n.map(this,function(e){return e[t]})},show:function(){return this.each(function(){"none"==this.style.display&&(this.style.display=""),"none"==getComputedStyle(this,"").getPropertyValue("display")&&(this.style.display=I(this.nodeName))})},replaceWith:function(t){return this.before(t).remove()},wrap:function(t){var e=Z(t);if(this[0]&&!e){var i=n(t).get(0),r=i.parentNode||this.length>1}return this.each(function(o){n(this).wrapAll(e?t.call(this,o):r?i.cloneNode(!0):i)})},wrapAll:function(t){if(this[0]){n(this[0]).before(t=n(t));for(var e;(e=t.children()).length;){t=e.first()}n(t).append(this)}return this},wrapInner:function(t){var e=Z(t);return this.each(function(i){var r=n(this),o=r.contents(),s=e?t.call(this,i):t;o.length?o.wrapAll(s):r.append(s)})},unwrap:function(){return this.parent().each(function(){n(this).replaceWith(n(this).children())}),this},clone:function(){return this.map(function(){return this.cloneNode(!0)})},hide:function(){return this.css("display","none")},toggle:function(e){return this.each(function(){var i=n(this);(e===t?"none"==i.css("display"):e)?i.show():i.hide()})},prev:function(t){return n(this.pluck("previousElementSibling")).filter(t||"*")},next:function(t){return n(this.pluck("nextElementSibling")).filter(t||"*")},html:function(t){return 0 in arguments?this.each(function(e){var i=this.innerHTML;n(this).empty().append(J(this,t,e,i))}):0 in this?this[0].innerHTML:null},text:function(t){return 0 in arguments?this.each(function(e){var n=J(this,t,e,this.textContent);this.textContent=null==n?"":""+n}):0 in this?this[0].textContent:null},attr:function(n,i){var r;return"string"!=typeof n||1 in arguments?this.each(function(t){if(1===this.nodeType){if(D(n)){for(e in n){X(this,e,n[e])}}else{X(this,n,J(this,i,t,this.getAttribute(n)))}}}):this.length&&1===this[0].nodeType?!(r=this[0].getAttribute(n))&&n in this[0]?this[0][n]:r:t},removeAttr:function(t){return this.each(function(){1===this.nodeType&&X(this,t)})},prop:function(t,e){return t=P[t]||t,1 in arguments?this.each(function(n){this[t]=J(this,e,n,this[t])}):this[0]&&this[0][t]},data:function(e,n){var i="data-"+e.replace(m,"-$1").toLowerCase(),r=1 in arguments?this.attr(i,n):this.attr(i);return null!==r?Y(r):t},val:function(t){return 0 in arguments?this.each(function(e){this.value=J(this,t,e,this.value)}):this[0]&&(this[0].multiple?n(this[0]).find("option").filter(function(){return this.selected}).pluck("value"):this[0].value)},offset:function(t){if(t){return this.each(function(e){var i=n(this),r=J(this,t,e,i.offset()),o=i.offsetParent().offset(),s={top:r.top-o.top,left:r.left-o.left};"static"==i.css("position")&&(s.position="relative"),i.css(s)})}if(!this.length){return null}var e=this[0].getBoundingClientRect();return{left:e.left+window.pageXOffset,top:e.top+window.pageYOffset,width:Math.round(e.width),height:Math.round(e.height)}},css:function(t,i){if(arguments.length<2){var r=this[0],o=getComputedStyle(r,"");if(!r){return}if("string"==typeof t){return r.style[C(t)]||o.getPropertyValue(t)}if(A(t)){var s={};return n.each(A(t)?t:[t],function(t,e){s[e]=r.style[C(e)]||o.getPropertyValue(e)}),s}}var a="";if("string"==L(t)){i||0===i?a=F(t)+":"+H(t,i):this.each(function(){this.style.removeProperty(F(t))})}else{for(e in t){t[e]||0===t[e]?a+=F(e)+":"+H(e,t[e])+";":this.each(function(){this.style.removeProperty(F(e))})}}return this.each(function(){this.style.cssText+=";"+a})},index:function(t){return t?this.indexOf(n(t)[0]):this.parent().children().indexOf(this[0])},hasClass:function(t){return t?r.some.call(this,function(t){return this.test(W(t))},q(t)):!1},addClass:function(t){return t?this.each(function(e){i=[];var r=W(this),o=J(this,t,e,r);o.split(/\s+/g).forEach(function(t){n(this).hasClass(t)||i.push(t)},this),i.length&&W(this,r+(r?" ":"")+i.join(" "))}):this},removeClass:function(e){return this.each(function(n){return e===t?W(this,""):(i=W(this),J(this,e,n,i).split(/\s+/g).forEach(function(t){i=i.replace(q(t)," ")}),void W(this,i.trim()))})},toggleClass:function(e,i){return e?this.each(function(r){var o=n(this),s=J(this,e,r,W(this));s.split(/\s+/g).forEach(function(e){(i===t?!o.hasClass(e):i)?o.addClass(e):o.removeClass(e)})}):this},scrollTop:function(e){if(this.length){var n="scrollTop" in this[0];return e===t?n?this[0].scrollTop:this[0].pageYOffset:this.each(n?function(){this.scrollTop=e}:function(){this.scrollTo(this.scrollX,e)})}},scrollLeft:function(e){if(this.length){var n="scrollLeft" in this[0];return e===t?n?this[0].scrollLeft:this[0].pageXOffset:this.each(n?function(){this.scrollLeft=e}:function(){this.scrollTo(e,this.scrollY)})}},position:function(){if(this.length){var t=this[0],e=this.offsetParent(),i=this.offset(),r=d.test(e[0].nodeName)?{top:0,left:0}:e.offset();return i.top-=parseFloat(n(t).css("margin-top"))||0,i.left-=parseFloat(n(t).css("margin-left"))||0,r.top+=parseFloat(n(e[0]).css("border-top-width"))||0,r.left+=parseFloat(n(e[0]).css("border-left-width"))||0,{top:i.top-r.top,left:i.left-r.left}}},offsetParent:function(){return this.map(function(){for(var t=this.offsetParent||a.body;t&&!d.test(t.nodeName)&&"static"==n(t).css("position");){t=t.offsetParent}return t})}},n.fn.detach=n.fn.remove,["width","height"].forEach(function(e){var i=e.replace(/./,function(t){return t[0].toUpperCase()});n.fn[e]=function(r){var o,s=this[0];return r===t?$(s)?s["inner"+i]:_(s)?s.documentElement["scroll"+i]:(o=this.offset())&&o[e]:this.each(function(t){s=n(this),s.css(e,J(this,r,t,s[e]()))})}}),v.forEach(function(t,e){var i=e%2;n.fn[t]=function(){var t,o,r=n.map(arguments,function(e){return t=L(e),"object"==t||"array"==t||null==e?e:T.fragment(e)}),s=this.length>1;return r.length<1?this:this.each(function(t,u){o=i?u:u.parentNode,u=0==e?u.nextSibling:1==e?u.firstChild:2==e?u:null;var f=n.contains(a.documentElement,o);r.forEach(function(t){if(s){t=t.cloneNode(!0)}else{if(!o){return n(t).remove()}}o.insertBefore(t,u),f&&G(t,function(t){null==t.nodeName||"SCRIPT"!==t.nodeName.toUpperCase()||t.type&&"text/javascript"!==t.type||t.src||window.eval.call(window,t.innerHTML)})})})},n.fn[i?t+"To":"insert"+(e?"Before":"After")]=function(e){return n(e)[t](this),this}}),T.Z.prototype=n.fn,T.uniq=N,T.deserializeValue=Y,n.zepto=T,n}();window.Zepto=Zepto,void 0===window.$&&(window.$=Zepto),function(F){function M(a){return a._zid||(a._zid=T++)}function P(b,d,f,a){if(d=I(d),d.ns){var c=U(d.ns)}return(G[M(b)]||[]).filter(function(e){return !(!e||d.e&&e.e!=d.e||d.ns&&!c.test(e.ns)||f&&M(e.fn)!==M(f)||a&&e.sel!=a)})}function I(a){var b=(""+a).split(".");return{e:b[0],ns:b.slice(1).sort().join(" ")}}function U(a){return new RegExp("(?:^| )"+a.replace(" "," .* ?")+"(?: |$)")}function L(a,b){return a.del&&!C&&a.e in R||!!b}function Q(a){return V[a]||C&&R[a]||a}function B(n,j,b,c,s,t,m){var l=M(n),p=G[l]||(G[l]=[]);j.split(/\s/).forEach(function(d){if("ready"==d){return F(document).ready(b)}var e=I(d);e.fn=b,e.sel=s,e.e in V&&(b=function(f){var h=f.relatedTarget;return !h||h!==this&&!F.contains(this,h)?e.fn.apply(this,arguments):void 0}),e.del=t;var a=t||b;e.proxy=function(h){if(h=N(h),!h.isImmediatePropagationStopped()){h.data=c;var f=a.apply(n,h._args==K?[h]:[h].concat(h._args));return f===!1&&(h.preventDefault(),h.stopPropagation()),f}},e.i=p.length,p.push(e),"addEventListener" in n&&n.addEventListener(Q(e.e),e.proxy,L(e,m))})}function q(b,d,h,a,c){var f=M(b);(d||"").split(/\s/).forEach(function(i){P(b,i,h,a).forEach(function(j){delete G[f][j.i],"removeEventListener" in b&&b.removeEventListener(Q(j.e),j.proxy,L(j,c))})})}function N(b,a){return(a||!b.isDefaultPrevented)&&(a||(a=b),F.each(D,function(c,e){var d=a[c];b[c]=function(){return this[e]=z,d&&d.apply(a,arguments)},b[e]=W}),(a.defaultPrevented!==K?a.defaultPrevented:"returnValue" in a?a.returnValue===!1:a.getPreventDefault&&a.getPreventDefault())&&(b.isDefaultPrevented=z)),b}function k(b){var c,a={originalEvent:b};for(c in b){A.test(c)||b[c]===K||(a[c]=b[c])}return N(a,b)}var K,T=1,O=Array.prototype.slice,H=F.isFunction,J=function(a){return"string"==typeof a},G={},X={},C="onfocusin" in window,R={focus:"focusin",blur:"focusout"},V={mouseenter:"mouseover",mouseleave:"mouseout"};X.click=X.mousedown=X.mouseup=X.mousemove="MouseEvents",F.event={add:B,remove:q},F.proxy=function(d,f){var c=2 in arguments&&O.call(arguments,2);if(H(d)){var b=function(){return d.apply(f,c?c.concat(O.call(arguments)):arguments)};return b._zid=M(d),b}if(J(f)){return c?(c.unshift(d[f],d),F.proxy.apply(null,c)):F.proxy(d[f],d)}throw new TypeError("expected function")},F.fn.bind=function(a,b,c){return this.on(a,b,c)},F.fn.unbind=function(a,b){return this.off(a,b)},F.fn.one=function(b,c,d,a){return this.on(b,c,d,a,1)};var z=function(){return !0},W=function(){return !1},A=/^([A-Z]|returnValue$|layer[XY]$)/,D={preventDefault:"isDefaultPrevented",stopImmediatePropagation:"isImmediatePropagationStopped",stopPropagation:"isPropagationStopped"};F.fn.delegate=function(a,b,c){return this.on(b,a,c)},F.fn.undelegate=function(a,b,c){return this.off(b,a,c)},F.fn.live=function(a,b){return F(document.body).delegate(this.selector,a,b),this},F.fn.die=function(a,b){return F(document.body).undelegate(this.selector,a,b),this},F.fn.on=function(o,m,d,i,n){var p,b,j=this;return o&&!J(o)?(F.each(o,function(a,c){j.on(a,m,d,c,n)}),j):(J(m)||H(i)||i===!1||(i=d,d=m,m=K),(H(d)||d===!1)&&(i=d,d=K),i===!1&&(i=W),j.each(function(c,a){n&&(p=function(e){return q(a,e.type,i),i.apply(this,arguments)}),m&&(b=function(f){var l,h=F(f.target).closest(m,a).get(0);return h&&h!==a?(l=F.extend(k(f),{currentTarget:h,liveFired:a}),(p||i).apply(h,[l].concat(O.call(arguments,1)))):void 0}),B(a,o,i,d,m,b||p)}))},F.fn.off=function(f,c,d){var b=this;return f&&!J(f)?(F.each(f,function(a,h){b.off(a,c,h)}),b):(J(c)||H(d)||d===!1||(d=c,c=K),d===!1&&(d=W),b.each(function(){q(this,f,d,c)}))},F.fn.trigger=function(a,b){return a=J(a)||F.isPlainObject(a)?F.Event(a):N(a),a._args=b,this.each(function(){"dispatchEvent" in this?this.dispatchEvent(a):F(this).triggerHandler(a,b)})},F.fn.triggerHandler=function(c,d){var a,b;return this.each(function(f,e){a=k(J(c)?F.Event(c):c),a._args=d,a.target=e,F.each(P(e,c.type||c),function(h,i){return b=i.proxy(a),a.isImmediatePropagationStopped()?!1:void 0})}),b},"focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select keydown keypress keyup error".split(" ").forEach(function(a){F.fn[a]=function(b){return b?this.bind(a,b):this.trigger(a)}}),["focus","blur"].forEach(function(a){F.fn[a]=function(b){return b?this.bind(a,b):this.each(function(){try{this[a]()}catch(c){}}),this}}),F.Event=function(b,d){J(b)||(d=b,b=d.type);var f=document.createEvent(X[b]||"Events"),a=!0;if(d){for(var c in d){"bubbles"==c?a=!!d[c]:f[c]=d[c]}}return f.initEvent(b,a,!0),N(f)}}(Zepto),function(t){function l(e,n,i){var r=t.Event(n);return t(e).trigger(r,i),!r.isDefaultPrevented()}function h(t,e,i,r){return t.global?l(e||n,i,r):void 0}function p(e){e.global&&0===t.active++&&h(e,null,"ajaxStart")}function d(e){e.global&&!--t.active&&h(e,null,"ajaxStop")}function m(t,e){var n=e.context;return e.beforeSend.call(n,t,e)===!1||h(e,n,"ajaxBeforeSend",[t,e])===!1?!1:void h(e,n,"ajaxSend",[t,e])}function g(t,e,n,i){var r=n.context,o="success";n.success.call(r,t,o,e),i&&i.resolveWith(r,[t,o,e]),h(n,r,"ajaxSuccess",[e,n,t]),y(o,e,n)}function v(t,e,n,i,r){var o=i.context;i.error.call(o,n,e,t),r&&r.rejectWith(o,[n,e,t]),h(i,o,"ajaxError",[n,i,t||e]),y(e,n,i)}function y(t,e,n){var i=n.context;n.complete.call(i,e,t),h(n,i,"ajaxComplete",[e,n]),d(n)}function x(){}function b(t){return t&&(t=t.split(";",2)[0]),t&&(t==f?"html":t==u?"json":s.test(t)?"script":a.test(t)&&"xml")||"text"}function w(t,e){return""==e?t:(t+"&"+e).replace(/[&?]{1,2}/,"?")}function E(e){e.processData&&e.data&&"string"!=t.type(e.data)&&(e.data=t.param(e.data,e.traditional)),!e.data||e.type&&"GET"!=e.type.toUpperCase()||(e.url=w(e.url,e.data),e.data=void 0)}function j(e,n,i,r){return t.isFunction(n)&&(r=i,i=n,n=void 0),t.isFunction(i)||(r=i,i=void 0),{url:e,data:n,success:i,dataType:r}}function T(e,n,i,r){var o,s=t.isArray(n),a=t.isPlainObject(n);t.each(n,function(n,u){o=t.type(u),r&&(n=i?r:r+"["+(a||"object"==o||"array"==o?n:"")+"]"),!r&&s?e.add(u.name,u.value):"array"==o||!i&&"object"==o?T(e,u,i,n):e.add(n,u)})}var i,r,e=0,n=window.document,o=/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,s=/^(?:text|application)\/javascript/i,a=/^(?:text|application)\/xml/i,u="application/json",f="text/html",c=/^\s*$/;t.active=0,t.ajaxJSONP=function(i,r){if(!("type" in i)){return t.ajax(i)}var f,h,o=i.jsonpCallback,s=(t.isFunction(o)?o():o)||"jsonp"+ ++e,a=n.createElement("script"),u=window[s],c=function(e){t(a).triggerHandler("error",e||"abort")},l={abort:c};return r&&r.promise(l),t(a).on("load error",function(e,n){clearTimeout(h),t(a).off().remove(),"error"!=e.type&&f?g(f[0],l,i,r):v(null,n||"error",l,i,r),window[s]=u,f&&t.isFunction(u)&&u(f[0]),u=f=void 0}),m(l,i)===!1?(c("abort"),l):(window[s]=function(){f=arguments},a.src=i.url.replace(/\?(.+)=\?/,"?$1="+s),n.head.appendChild(a),i.timeout>0&&(h=setTimeout(function(){c("timeout")},i.timeout)),l)},t.ajaxSettings={type:"GET",beforeSend:x,success:x,error:x,complete:x,context:null,global:!0,xhr:function(){return new window.XMLHttpRequest},accepts:{script:"text/javascript, application/javascript, application/x-javascript",json:u,xml:"application/xml, text/xml",html:f,text:"text/plain"},crossDomain:!1,timeout:0,processData:!0,cache:!0},t.ajax=function(e){var n=t.extend({},e||{}),o=t.Deferred&&t.Deferred();for(i in t.ajaxSettings){void 0===n[i]&&(n[i]=t.ajaxSettings[i])}p(n),n.crossDomain||(n.crossDomain=/^([\w-]+:)?\/\/([^\/]+)/.test(n.url)&&RegExp.$2!=window.location.host),n.url||(n.url=window.location.toString()),E(n);var s=n.dataType,a=/\?.+=\?/.test(n.url);if(a&&(s="jsonp"),n.cache!==!1&&(e&&e.cache===!0||"script"!=s&&"jsonp"!=s)||(n.url=w(n.url,"_="+Date.now())),"jsonp"==s){return a||(n.url=w(n.url,n.jsonp?n.jsonp+"=?":n.jsonp===!1?"":"callback=?")),t.ajaxJSONP(n,o)}var j,u=n.accepts[s],f={},l=function(t,e){f[t.toLowerCase()]=[t,e]},h=/^([\w-]+:)\/\//.test(n.url)?RegExp.$1:window.location.protocol,d=n.xhr(),y=d.setRequestHeader;if(o&&o.promise(d),n.crossDomain||l("X-Requested-With","XMLHttpRequest"),l("Accept",u||"*/*"),(u=n.mimeType||u)&&(u.indexOf(",")>-1&&(u=u.split(",",2)[0]),d.overrideMimeType&&d.overrideMimeType(u)),(n.contentType||n.contentType!==!1&&n.data&&"GET"!=n.type.toUpperCase())&&l("Content-Type",n.contentType||"application/x-www-form-urlencoded"),n.headers){for(r in n.headers){l(r,n.headers[r])}}if(d.setRequestHeader=l,d.onreadystatechange=function(){if(4==d.readyState){d.onreadystatechange=x,clearTimeout(j);var e,i=!1;if(d.status>=200&&d.status<300||304==d.status||0==d.status&&"file:"==h){s=s||b(n.mimeType||d.getResponseHeader("content-type")),e=d.responseText;try{"script"==s?(1,eval)(e):"xml"==s?e=d.responseXML:"json"==s&&(e=c.test(e)?null:t.parseJSON(e))}catch(r){i=r}i?v(i,"parsererror",d,n,o):g(e,d,n,o)}else{v(d.statusText||null,d.status?"error":"abort",d,n,o)}}},m(d,n)===!1){return d.abort(),v(null,"abort",d,n,o),d}if(n.xhrFields){for(r in n.xhrFields){d[r]=n.xhrFields[r]}}var S="async" in n?n.async:!0;d.open(n.type,n.url,S,n.username,n.password);for(r in f){y.apply(d,f[r])}return n.timeout>0&&(j=setTimeout(function(){d.onreadystatechange=x,d.abort(),v(null,"timeout",d,n,o)},n.timeout)),d.send(n.data?n.data:null),d},t.get=function(){return t.ajax(j.apply(null,arguments))},t.post=function(){var e=j.apply(null,arguments);return e.type="POST",t.ajax(e)},t.getJSON=function(){var e=j.apply(null,arguments);return e.dataType="json",t.ajax(e)},t.fn.load=function(e,n,i){if(!this.length){return this}var a,r=this,s=e.split(/\s/),u=j(e,n,i),f=u.success;return s.length>1&&(u.url=s[0],a=s[1]),u.success=function(e){r.html(a?t("<div>").html(e.replace(o,"")).find(a):e),f&&f.apply(r,arguments)},t.ajax(u),this};var S=encodeURIComponent;t.param=function(t,e){var n=[];return n.add=function(t,e){this.push(S(t)+"="+S(e))},T(n,t,e),n.join("&").replace(/%20/g,"+")}}(Zepto),function(a){a.fn.serializeArray=function(){var c,b=[];return a([].slice.call(this.get(0).elements)).each(function(){c=a(this);var d=c.attr("type");"fieldset"!=this.nodeName.toLowerCase()&&!this.disabled&&"submit"!=d&&"reset"!=d&&"button"!=d&&("radio"!=d&&"checkbox"!=d||this.checked)&&b.push({name:c.attr("name"),value:c.val()})}),b},a.fn.serialize=function(){var b=[];return this.serializeArray().forEach(function(c){b.push(encodeURIComponent(c.name)+"="+encodeURIComponent(c.value))}),b.join("&")},a.fn.submit=function(b){if(b){this.bind("submit",b)}else{if(this.length){var c=a.Event("submit");this.eq(0).trigger(c),c.isDefaultPrevented()||this.get(0).submit()}}return this}}(Zepto),function(a){"__proto__" in {}||a.extend(a.zepto,{Z:function(d,f){return d=d||[],a.extend(d,a.fn),d.selector=f||"",d.__Z=!0,d},isZ:function(d){return"array"===a.type(d)&&"__Z" in d}});try{getComputedStyle(void 0)}catch(b){var c=getComputedStyle;window.getComputedStyle=function(d){try{return c(d)}catch(f){return null}}}}(Zepto);(function(t){var v={},z,q,u,B,x=750,p;function s(c,d,a,b){return Math.abs(c-d)>=Math.abs(a-b)?(c-d>0?"Left":"Right"):(a-b>0?"Up":"Down")}function o(){B=null;if(v.last){v.el.trigger("longTap");v={}}}function y(){if(B){clearTimeout(B)}B=null}function w(){if(z){clearTimeout(z)}if(q){clearTimeout(q)}if(u){clearTimeout(u)}if(B){clearTimeout(B)}z=q=u=B=null;v={}}function r(a){return(a.pointerType=="touch"||a.pointerType==a.MSPOINTER_TYPE_TOUCH)&&a.isPrimary}function A(a,b){return(a.type=="pointer"+b||a.type.toLowerCase()=="mspointer"+b)}t(document).ready(function(){var d,a,e=0,f=0,b,c;if("MSGesture" in window){p=new MSGesture();p.target=document.body}t(document).bind("MSGestureEnd",function(h){var i=h.velocityX>1?"Right":h.velocityX<-1?"Left":h.velocityY>1?"Down":h.velocityY<-1?"Up":null;if(i){v.el.trigger("swipe");v.el.trigger("swipe"+i)}}).on("touchstart MSPointerDown",function(h){if((c=A(h,"down"))&&!r(h)){return}b=c?h:h.touches[0];if(h.touches&&h.touches.length===1&&v.x2){v.x2=undefined;v.y2=undefined}d=Date.now();a=d-(v.last||d);v.el=t("tagName" in b.target?b.target:b.target.parentNode);z&&clearTimeout(z);v.x1=b.pageX;v.y1=b.pageY;if(a>0&&a<=250){v.isDoubleTap=true}v.last=d;B=setTimeout(o,x);if(p&&c){p.addPointer(h.pointerId)}}).on("touchmove MSPointerMove",function(h){if((c=A(h,"move"))&&!r(h)){return}b=c?h:h.touches[0];y();v.x2=b.pageX;v.y2=b.pageY;e+=Math.abs(v.x1-v.x2);f+=Math.abs(v.y1-v.y2)}).on("touchend MSPointerUp",function(h){if((c=A(h,"up"))&&!r(h)){return}y();if((v.x2&&Math.abs(v.x1-v.x2)>30)||(v.y2&&Math.abs(v.y1-v.y2)>30)){u=setTimeout(function(){if(v.el){v.el.trigger("swipe");v.el.trigger("swipe"+(s(v.x1,v.x2,v.y1,v.y2)))}v={}},0)}else{if("last" in v){if(e<30&&f<30){q=setTimeout(function(){var i=t.Event("tap");i.cancelTouch=w;if(v.el){v.el.trigger(i)}if(v.isDoubleTap){if(v.el){v.el.trigger("doubleTap")}v={}}else{z=setTimeout(function(){z=null;if(v.el){v.el.trigger("singleTap")}v={}},250)}},0)}else{v={}}}}e=f=0}).on("touchcancel MSPointerCancel",w);t(window).on("scroll",w)});["swipe","swipeLeft","swipeRight","swipeUp","swipeDown","doubleTap","tap","singleTap","longTap"].forEach(function(a){t.fn[a]=function(b){return this.on(a,b)}})})(Zepto);(function(b){b.g=function(e,d){var f=new RegExp("(^|&)"+e+"=([^&]*)(&|$)");var i=location.search.substr(1);if(d){var c=d.split("?");i=c[1]||""}var h=i.match(f);return h?decodeURIComponent(h[2]):""};b.getImgUrl=function(d,c){if(!d){return null}d=d.split(",")[0];if(d.substr(0,4).toLowerCase()=="http"){return d}else{return"http://"+d.replace(/\//,".fit-time.cn/")+(c?c:"")}};b.gs=function(h,f){var i=new RegExp("(^|[?&])"+h+"=([^&]*)","g");var k=location.search.substr(1);if(f){var d=f.split("?");if(d.length>1){k=d[1]}}var j=k.match(i);if(j){var c=[];for(var e in j){c.push(j[e].replace(/^.+=/,""))}return c}return null};b.getAsPhoto=function(k,f,l){if(!k||k.length==0){return null}if(f==0||l==0){return k[0]}if(typeof k=="string"){k=JSON.parse(k)}var i=f/l;var d;var h;for(var j in k){var e=j.split("X");if(e&&e.length==2&&e[0]!=0&&e[1]!=0){var c=e[0]/e[1];if(!h||Math.abs(c-i)<Math.abs(h-i)){h=c;d=k[j]}}}return d};function a(){this.init()}a.prototype.init=function(){this.inWeixin=navigator.userAgent.toLowerCase().match(/MicroMessenger/i)=="micromessenger"?true:false;var e=["Android","iPhone","SymbianOS","Windows Phone","iPad","iPod"];var c=false;for(var d=0;d<e.length;d++){if(navigator.userAgent.indexOf(e[d])>0){c=true;break}}this.isMobile=c};a.prototype.showLoading=function(c,f){if(this.loading){this.hideLoading()}var e=document.createElement("div");var d="width:100%;height:100%;left:0;top:0;z-index:9998;";if(f){this.loadingDom=document.getElementById(f);d+="position:absolute;";this.loadingDomPosition=$(this.loadingDom).css("position");if(this.loadingDomPosition=="static"){$(this.loadingDom).css("position","relative")}}else{this.loadingDom=document.body;d+="position:fixed;"}d+=c?"background:rgba(0,0,0,.3);":"background:none;";e.style.cssText=d;e.innerHTML='<style>@keyframes asdfasffdgsd{to{transform: rotate(360deg)}}@-webkit-keyframes asdfasffdgsd{to{-webkit-transform: rotate(360deg)}}</style><div style="position:absolute;left:0;top:0;right:0;bottom:0;margin:auto;width:50px;height:50px;background:none;border:8px solid #eee;border-radius:50%;border-right-color:#999;-webkit-animation:asdfasffdgsd linear 1.5s infinite;animation:asdfasffdgsd linear 1.5s infinite;"></div>';this.loadingDom.appendChild(e);if(this.isMobile){e.addEventListener("touchmove",function(h){h.preventDefault()},false)}this.loading=e};a.prototype.hideLoading=function(){if(this.loading){this.loadingDom.removeChild(this.loading);if(this.loadingDomPosition=="static"){$(this.loadingDom).css("position","static")}this.loading=false}};a.prototype.log=function(d,c){$.ajax({url:"http://api.fit-time.cn/ftinfo/jp_webLog?key="+d,dataType:"jsonp",success:function(){if(c){c()}}})};a.prototype.alert=function(c,d){if(this.toastIng){return}this.showToast(c,1,d)};a.prototype.confirm=function(c,d){if(this.toastIng){return}this.showToast(c,0,d)};a.prototype.showToast=function(f,m,o){var q=document.createElement("div");q.style.cssText="position:fixed;width:100%;height: 100%;background:rgba(0,0,0,.7);left:0;top:0;font-size:16px;color:#3a3c48;text-align:center;z-index:9999";q.addEventListener("touchmove",function(r){r.preventDefault()});var k="确定",p="确定",n="取消",l="undefine",d=1,c="#fff",e="#d8d8da";if(f){if(Object.prototype.toString.call(f)=="[object String]"||!isNaN(f)){l=f}else{d=0;if(f.content){l=f.content}if(f.btn){k=f.btn}if(f.btnLeft){p=f.btnLeft}if(f.btnRight){n=f.btnRight}if(f.background){c=f.background}if(f.borderColor){e=f.borderColor}}}var h='<div style="width:100%;height:100%;display:table"><div style="width:100%;display:table-cell;vertical-align:middle"><div style="width:80%;max-width:360px;border-radius:5px;background:'+c+';margin:0 auto"><div style="line-height:1.6;padding:20px 15px;position:relative;word-break:break-all">'+l+'<span style="position:absolute;width:100%;height:1px;background:'+e+';left:0;bottom:0;-webkit-transform:scaleY(0.5);transform:scaleY(0.5)"></span></div>';if(m){h+='<div style="line-height:3;height:3em;cursor:pointer"><div id="skerofsddo1" style="color:#3a59f5">'+k+"</div></div></div></div></div>"}else{h+='<div style="line-height:3;height:3em;cursor:pointer"><div id="skerofsddo2" style="color:#3a59f5;width:50%;float:left;position:relative">'+p+'<span style="position:absolute;width:1px;height:100%;background:'+e+';right:0;bottom:0;-webkit-transform:scaleX(0.5);transform:scaleX(0.5)"></span></div><div id="skerofsddo3" style="color:#999;width:50%;float:left">'+n+"</div></div></div></div></div>"}q.innerHTML=h;document.body.appendChild(q);this.toastIng=true;var i=this.isMobile?"touchend":"click";var j=this;document.getElementById("skerofsddo"+(m?1:2)).addEventListener(i,function(r){r.preventDefault();document.body.removeChild(q);j.toastIng=false;if(!d&&f.callback){f.callback()}else{if(o){o()}}});if(!m){document.getElementById("skerofsddo3").addEventListener(i,function(r){r.preventDefault();document.body.removeChild(q);j.toastIng=false;if(!d&&f.cancelCallback){f.cancelCallback()}})}};a.prototype.parseUrl=function(e,d,f){var h=e.substring(0,5)=="rf://"?true:false;var i=g("token",e)?true:false;var c=e.indexOf("?")===-1?true:false;if(b.RockFitJSBridge){if(!h){if(!i){e+=c?"?token=":"&token="}e="rf://local/newWebPage?url="+encodeURIComponent(e);if(d){e+="&FTRFStyle="+d}}}else{if(h){e=e.replace(/rf:\//,"http://a2.fit-time.com")}else{if(!i&&f){e+=(c?"?token=":"&token=")+encodeURIComponent(f)}}}return e};b.FT=new a()}(window));