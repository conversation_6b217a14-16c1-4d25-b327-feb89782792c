<template>
    <!-- left nav item for has treeview -->
    <li class="nav-item has-treeview">
        <a href="javascript:;" class="nav-link">
            <i class="nav-icon fa" :class="[router.meta.icon]"></i>
            <p>
                {{ router.meta.title }}
                <i class="fa fa-angle-left right"></i>
            </p>
        </a>
        <ul class="nav nav-treeview">
            <template v-for="(item, index) in router.children">
                <li class="nav-item" v-if="item.meta.showInMenu" :key="index">
                    <router-link :to="`${router.path}/${item.path}`" class="nav-link">
                        <i class="fa nav-icon" :class="[item.meta.icon]"></i>
                        <p>{{ item.meta.title }}</p>
                    </router-link>
                </li>
            </template>
        </ul>
    </li>
</template>

<script>
export default {
    name: 'NavItem',
    props: {
        router: Object
    }
}
</script>