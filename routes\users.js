var express = require('express')
var router = express.Router()
const userController = require('../controllers/userController')
const logController = require('../controllers/logController')
const { USER_TYPE } = require('../constant/enum')

router.post('/login', function (req, res, next) {
	const { mobile, password, wxUnionId, ksUnionId, dyUnionId } = req.body
	if (wxUnionId || ksUnionId || dyUnionId || (mobile && password)) {
		let task
		if (wxUnionId) {
			task = userController.wxLogin(req.body)
		} else if (ksUnionId) {
			task = userController.ksLogin(req.body)
		} else if (dyUnionId) {
			task = userController.dyLogin(req.body)
		} else {
			task = userController.mobileLogin(mobile, password)
		}
		task
			.then(data => {
				delete data.password
				req.session['userId'] = data.id
				req.session['isBuyer'] = data.type === USER_TYPE.buyer
				req.session['isCollector'] = data.type === USER_TYPE.collector
				req.session['isAdmin'] = data.type === USER_TYPE.admin
				res.sendSuccess(data)
			})
			.catch(err => {
				res.sendMessage(err)
			})
	} else {
		res.sendMessage('参数有误')
	}
})

router.post('/mini/loginByCode', function (req, res, next) {
	const { code, inviteUserId } = req.body
	const { platform } = req.query
	if (code && platform && ['mp-weixin', 'mp-toutiao', 'mp-kuaishou'].includes(platform)) {
		userController
			.getMiniUserByCode(code, platform)
			.then(miniUser => {
				if (miniUser) {
					console.log('小程序用户', miniUser)
					userController
						.queryOrAddMiniUser(miniUser, inviteUserId, platform)
						.then(data => {
							delete data.password
							req.session['userId'] = data.id
							req.session['isBuyer'] = data.type === USER_TYPE.buyer
							req.session['isCollector'] = data.type === USER_TYPE.collector
							req.session['isAdmin'] = data.type === USER_TYPE.admin
							res.sendSuccess(data)
						})
						.catch(err => {
							console.log('获取用户失败', err)
							res.sendMessage(err)
						})
				} else {
					res.sendMessage('未能获取小程序账号信息')
				}
			})
			.catch(err => {
				console.log('获取小程序用户失败', err)
				res.sendMessage(err)
			})
	} else {
		res.sendMessage('参数有误')
	}
})

router.get('/getCurrentUser', function (req, res, next) {
	const { userId } = req.body
	userController
		.getUserInfo(userId)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.post('/mini/getWxUser', function (req, res, next) {
	const { code, encryptedData, iv } = req.body
	userController
		.getMiniWxUser(code, encryptedData, iv)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.post('/mini/getWxPhone', function (req, res, next) {
	const { code } = req.body
	userController
		.getMiniWxPhone(code)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.post('/official/getWxUser', function (req, res, next) {
	const { code, encryptedData, iv } = req.body
	userController
		.getOfficialWxUser(code, encryptedData, iv)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.put('/updateMyInfo', function (req, res, next) {
	const { userId } = req.body
	if (userId) {
		userController
			.updateInfo(userId, req.body)
			.then(data => {
				res.sendSuccess(data)
			})
			.catch(err => {
				res.sendMessage(err)
			})
	} else {
		res.sendMessage('账号未登录')
	}
})

router.put('/updateInfoByCollector/:id', function (req, res, next) {
	// 信息收集员帮忙更新用户的地址、手机号、名字
	const { id } = req.params
	if (req.session.isCollector || req.session.isAdmin) {
		userController
			.updateInfo(id, req.body)
			.then(data => {
				res.sendSuccess(data)
			})
			.catch(err => {
				res.sendMessage(err)
			})
	}
})

router.get('/countLog/:key', function (req, res, next) {
	const { key } = req.params
	const { userId } = req.body
	logController
		.countLogs(key, userId)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			console.log(err)
			res.sendMessage(err)
		})
})

router.post('/getMyShareCount', function (req, res, next) {
	logController
		.countRecords(req.body)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			console.log(err)
			res.sendMessage(err)
		})
})

router.get('/getMyRole', function (req, res, next) {
	userController
		.getMyRoles(req.body.userId)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			console.log(err)
			res.sendMessage(err)
		})
})

router.get('/getMyLatestLog', function (req, res, next) {
	const { userId } = req.body
	const { key } = req.query
	if (!key) {
		res.sendMessage('参数有误')
		return
	}
	userController
		.getMyLogCountAndLatestLog(userId, key)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			console.log(err)
			res.sendMessage(err)
		})
})

module.exports = router
