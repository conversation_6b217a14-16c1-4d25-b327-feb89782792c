<template>
	<div class="card">
		<div class="search_wrapper">
			<el-select v-model="searchParams.online" placeholder="下单页状态" clearable>
				<el-option :value="1" label="下单页已上线"></el-option>
				<el-option :value="0" label="下单页已下线"></el-option>
			</el-select>
			<el-select v-model="searchParams.showPrice" placeholder="价格页状态" clearable>
				<el-option :value="1" label="价格页已上线"></el-option>
				<el-option :value="0" label="价格页已下线"></el-option>
			</el-select>
			<el-radio-group v-model="searchParams.ignoreChildren">
				<el-radio-button label="1">不含子类</el-radio-button>
				<el-radio-button label="0">包含子类</el-radio-button>
			</el-radio-group>
			<el-button type="primary" @click="loadData">查询</el-button>
			<el-button
				class="add_btn"
				type="primary"
				plain
				icon="el-icon-plus"
				@click="handleAddCrop('add')"
				>新增</el-button
			>
		</div>
		<div class="card-body">
			<el-table
				v-loading="loading"
				:data="renderList"
				border
				fit
				highlight-current-row
				style="width: 100%"
			>
				<el-table-column label="ID" prop="id" align="center" width="50" />
				<el-table-column label="作物" prop="name" align="center" width="90" />
				<el-table-column label="计量单位" prop="countUnit" align="center" width="100">
					<template slot-scope="{ row }">
						<div>下单：{{ row.countUnitOnCreate }}</div>
						<div>交易：{{ row.countUnit }}</div>
					</template>
				</el-table-column>
				<el-table-column label="计价单位" prop="priceUnit" align="center" width="100">
					<template slot-scope="{ row }">
						<div>下单：{{ row.priceUnitOnCreate }}</div>
						<div>交易：{{ row.priceUnit }}</div>
					</template>
				</el-table-column>
				<!-- <el-table-column label="下单最低价" prop="priceMin" align="center" width="100" />
                <el-table-column label="下单最高价" prop="priceMin" align="center" width="100" />-->
				<el-table-column label="排序值" prop="sortNumber" align="center" width="90" />
				<el-table-column label="默认展示湿度" prop="defaultHumidity" align="center" width="90" />
				<el-table-column label="信息填报过程" prop="collection_ids" align="center" />
				<el-table-column label="半挂车最低下单量" prop="considerWayMinCount" align="center" />
				<el-table-column label="是大单最低下单量" prop="bigOrderMinCount" align="center" />
				<el-table-column label="区域" prop="regionsName" align="center" />
				<el-table-column label="状态" prop="online" align="center">
					<template slot-scope="{ row }">
						<el-tag :type="row.online ? 'success' : 'danger'">
							{{ row.online ? '上线中' : '已下线' }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="操作" align="center" width="150">
					<template slot-scope="{ row }">
						<div class="btn_container">
							<el-button size="small" @click="editClick(row, 'edit')">作物编辑</el-button>
						</div>
						<div class="btn_container">
							<el-button size="small" @click="onRegionEditClick(row)">价格页区域配置</el-button>
						</div>
						<div class="btn_container">
							<el-button
								v-if="row.online"
								type="danger"
								plain
								size="small"
								@click="onOfflineClick(row)"
								>下单页下线</el-button
							>
							<el-button v-else type="success" plain size="small" @click="onOnlineClick(row)"
								>下单页上线</el-button
							>
						</div>
						<div class="btn_container">
							<el-button
								v-if="row.showPrice"
								type="danger"
								plain
								size="small"
								@click="onPricePageOfflineClick(row)"
								>价格页下线</el-button
							>
							<el-button
								v-else
								type="success"
								plain
								size="small"
								@click="onPricePageOnlineClick(row)"
								>价格页上线</el-button
							>
						</div>
						<div class="btn_container">
							<el-button
								size="small"
								type="danger"
								:disabled="!!row.online || !!row.showPrice"
								@click="onCropDelete(row)"
								>删除作物</el-button
							>
						</div>
					</template>
				</el-table-column>
			</el-table>
		</div>

		<el-dialog
			:title="editItem.id ? '编辑可下单作物' : '新增可下单作物'"
			:visible.sync="editDialog"
			:close-on-click-modal="false"
			:before-close="onEditBtnClose"
			width="800px"
		>
			<el-form
				:model="editItem"
				v-loading="updating"
				:rules="updateRules"
				ref="updateForm"
				label-width="130px"
			>
				<el-form-item label="作物名称" prop="name" required>
					<el-input v-model="editItem.name" placeholder="请输入作物名称"></el-input>
				</el-form-item>
				<el-form-item label="默认展示湿度" prop="name" required>
					<el-input-number
						v-model="editItem.defaultHumidity"
						placeholder="请填写默认展示湿度"
						:min="14"
						:max="40"
						style="display: block; width: 50%"
					/>
				</el-form-item>
				<el-row :gutter="24">
					<el-col :span="12">
						<el-form-item label="排序">
							<el-input-number v-model="editItem.sortNumber" :min="0" />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="更新时间节点延迟">
							<el-input-number v-model="editItem.dateSwitchDelay" :min="0" :max="23" />小时
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="24">
					<el-col :span="12">
						<el-form-item label="下单计量单位">
							<el-input v-model="editItem.countUnitOnCreate" :min="0"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="交易计量单位">
							<el-input v-model="editItem.countUnit" :min="0"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="24">
					<el-col :span="12">
						<el-form-item label="下单计价单位">
							<el-input v-model="editItem.priceUnitOnCreate" :min="0"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="交易计价单位">
							<el-input v-model="editItem.priceUnit" :min="0"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="24">
					<el-col :span="12">
						<el-form-item label="半挂车最低下单量">
							<el-input-number v-model="editItem.considerWayMinCount" :min="0"></el-input-number>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="是大单最低下单量">
							<el-input-number v-model="editItem.bigOrderMinCount" :min="0"></el-input-number>
						</el-form-item>
					</el-col>
				</el-row>
				<el-form-item label="作物整体视频描述">
					<el-input
						v-model="editItem.overallVideoMsg"
						placeholder="请输入作物整体视频描述"
					></el-input>
				</el-form-item>
				<el-form-item label="作物视频描述">
					<el-input v-model="editItem.videoMsg" placeholder="请输入作物视频描述"></el-input>
				</el-form-item>
				<el-form-item label="作物图片描述">
					<el-input v-model="editItem.photoMsg" placeholder="请输入作物图片描述"></el-input>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button @click="onEditBtnClose">取 消</el-button>
				<el-button type="primary" :loading="updating" @click="onEditBtnConfirm">确 定</el-button>
			</div>
		</el-dialog>

		<el-dialog
			title="编辑作物在价格页展示的区域"
			:visible.sync="regionEditDialogVisible"
			:close-on-click-modal="false"
			:before-close="onRegionClose"
			width="600px"
		>
			<el-form
				v-loading="updating"
				:model="editItem"
				:rules="updateRegionRules"
				ref="updateRegionForm"
				label-width="120px"
			>
				<el-form-item label="作物区域" prop="regions" required>
					<el-select
						v-model="editItem.regions"
						multiple
						placeholder="请选择作物区域"
						style="width: 100%"
						@change="onSelectRegionChange"
					>
						<el-option
							v-for="item in regions"
							:key="item.id"
							:label="item.name"
							:value="item.id"
						></el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="均价排除区域" prop="excludeRegions">
					<el-select
						v-model="editItem.excludeRegions"
						multiple
						:disabled="!selectRegions.length"
						placeholder="请选择作物均价计算排除区域"
						style="width: 100%"
					>
						<el-option
							v-for="item in selectRegions"
							:key="item.id"
							:label="item.name"
							:value="item.id"
						/>
					</el-select>
				</el-form-item>
			</el-form>
			<span slot="footer" class="dialog-footer">
				<el-button size="small" :loading="updating" type="primary" @click="onRegionConfirm"
					>确认</el-button
				>
				<el-button size="small" @click="onRegionClose">取消</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>
	import cropManager from '@/manager/cropManager'
	import tableMixin from '@/mixins/tableMixin'
	export default {
		name: 'OrderList',
		mixins: [tableMixin],
		data() {
			return {
				editDialog: false,
				regionEditDialogVisible: false,
				searchParams: {
					online: null,
					showPrice: null,
					ignoreChildren: 1
				},
				editItem: {},
				editType: '',
				regions: [],
				regionMaps: {},
				selectRegions: [],
				updating: false,
				updateRules: {
					name: [{ required: true, message: '请输入作物名称', trigger: ['blur', 'change'] }]
				},
				updateRegionRules: {
					regions: [{ required: true, message: '请选择作物区域', trigger: ['blur', 'change'] }]
				}
			}
		},
		computed: {
			renderList() {
				return this.tableData.map(item => {
					return {
						...item,
						regionsName: item?.regions
							?.split(',')
							.map(id => this.regionMaps[id] || id)
							.join(',')
					}
				})
			}
		},
		mounted() {
			this.queryRegions()
		},
		methods: {
			queryRegions() {
				cropManager.queryRegions().then(result => {
					this.regions = result
					this.regionMaps = result.reduce((map, item) => {
						map[item.id] = item.name
						return map
					}, {})
				})
			},
			loadData() {
				this.loading = true
				cropManager
					.queryCrops(this.searchParams)
					.then(res => {
						this.tableData = res
						this.loading = false
					})
					.catch(err => {
						this.$message.error(err)
						this.loading = false
					})
			},
			handleAddCrop(type) {
				this.editDialog = true
				this.editType = type
			},
			editClick(item, type) {
				this.editItem = { ...item }
				this.editType = type
				this.editDialog = true
			},
			async onEditBtnConfirm() {
				this.$refs['updateForm'].validate(async valid => {
					if (valid) {
						try {
							this.updating = true
							if (this.editType === 'add') await cropManager.addCrop(this.editItem)
							if (this.editType === 'edit')
								await cropManager.editCrop(this.editItem.id, this.editItem)
							this.$message.success(`作物信息${this.editType === 'add' ? '添加' : '更新'}成功`)
							this.loadData()
							this.onEditBtnClose()
						} catch (error) {
							this.$message.error(error.message)
						} finally {
							this.updating = false
						}
					} else {
						return false
					}
				})
			},
			onEditBtnClose() {
				this.editDialog = false
				this.editType = ''
				this.editItem = {}
				this.$refs['updateForm'].resetFields()
			},
			onOfflineClick(item) {
				this.$confirm('将该作物在下单页下线，是否继续', '操作提示')
					.then(() => {
						cropManager
							.editCrop(item.id, { online: 0 })
							.then(() => {
								this.$message.success('下单页下线成功')
								this.loadData()
							})
							.catch(err => this.$message.error(err))
					})
					.catch(() => {
						this.$message.info('操作已取消')
					})
			},
			onOnlineClick(item) {
				cropManager
					.editCrop(item.id, { online: 1 })
					.then(() => {
						this.$message.success('下单页上线成功')
						this.loadData()
					})
					.catch(err => this.$message.error(err))
			},
			onPricePageOfflineClick(item) {
				this.$confirm('将该作物在价格页下线，是否继续', '操作提示')
					.then(() => {
						cropManager
							.editCrop(item.id, { showPrice: 0 })
							.then(() => {
								this.$message.success('价格页下线成功')
								this.loadData()
							})
							.catch(err => this.$message.error(err))
					})
					.catch(() => {
						this.$message.info('操作已取消')
					})
			},
			onPricePageOnlineClick(item) {
				cropManager
					.editCrop(item.id, { showPrice: 1 })
					.then(() => {
						this.$message.success('价格页上线成功')
						this.loadData()
					})
					.catch(err => this.$message.error(err))
			},
			onCropDelete(row) {
				this.$confirm(`确认要删除作物：${row.name}吗？`, '操作提示')
					.then(() => {
						cropManager
							.deleteCrop(row.id)
							.then(() => {
								this.$message.success('删除成功')
								this.loadData()
							})
							.catch(err => {
								this.$message.error(err)
							})
					})
					.catch(() => {
						this.$message.info('操作已取消')
					})
			},
			onRegionEditClick(item) {
				this.regionEditDialogVisible = true
				this.editItem = { ...item }
				this.editItem.regions
				if (item.regions === '') {
					this.editItem.regions = []
				} else {
					this.editItem.regions = item.regions.split(',').map(Number)
				}
				// 筛选出编辑项中所选区域对应的地区信息，并排除 id 为 35 和 36 的地区
				this.selectRegions = this.regions.filter(item => {
					const isIncluded = this.editItem.regions.includes(item.id)
					const isNotExcluded = item.id !== 35 && item.id !== 36
					return isIncluded && isNotExcluded
				})
				this.editItem.excludeRegions = item.excludeRegions
					.split(',')
					.map(Number)
					.filter(id => id > 0)
			},
			rearrangeRegions(regions) {
				// 检查是否包含全国
				const has35 = regions.includes(35)
				// 检查是否包含其他
				const has36 = regions.includes(36)
				const filteredRegions = regions.filter(region => region !== 35 && region !== 36)
				if (has35) filteredRegions.unshift(35)
				if (has36) filteredRegions.push(36)

				return filteredRegions
			},

			onSelectRegionChange(values) {
				this.selectRegions = this.regions
					.filter(item => values.includes(item.id))
					.filter(item => item.id !== 35 && item.id !== 36)
			},

			onRegionConfirm() {
				this.$refs['updateRegionForm'].validate(async valid => {
					if (valid) {
						this.updating = true
						const { regions, excludeRegions } = this.editItem
						const params = {
							regions: this.rearrangeRegions(regions).join(','),
							excludeRegions: excludeRegions.length ? excludeRegions.join(',') : '-1'
						}
						cropManager
							.updateRegions(this.editItem.id, params)
							.then(() => {
								this.$message.success('作物价格区域更新成功')
								this.loadData()
								this.onRegionClose()
							})
							.catch(err => this.$message.error(err))
							.finally(() => {
								this.updating = false
							})
					} else {
						return false
					}
				})
			},
			onRegionClose() {
				this.editItem = {}
				this.regionEditDialogVisible = false
				this.$refs['updateRegionForm'].resetFields()
			}
		}
	}
</script>

<style lang="scss" scoped>
	.btn_container + .btn_container {
		margin-top: 10px;
	}
</style>
