// 城市列表控制
const http = require('http');
const iconv = require('iconv-lite');

const dbController = require('./dbController');
const { resFilter } = require('../utils/filter');
const { CITY_CODE } = require('../db').tableNames;

// 该方法为爬取国土统计局数据进行添加
function addChinaCity () {
    let provinceArr = [11, 12, 13, 14, 15, 21, 22, 23, 31, 32, 33, 34, 35, 36, 37, 41, 42, 43, 44, 45, 46, 50, 51, 52, 53, 54, 61, 62, 63, 64, 65];
    return new Promise(function (resolve, reject) {
        let addProvinceIndex = 0;
        addNext();
        function addNext () {
            addProvince(provinceArr[addProvinceIndex], () => {
                addProvinceIndex++;
                if (addProvinceIndex === provinceArr.length) {
                    resolve('添加完毕')
                } else {
                    addNext();
                }
            }, err => {
                reject(err);
            })
        }
    })
}

function addProvince (id, finishCallback, failCallback) {
    let provincePath = '/' + id + '.html';
    queryCityHtml(provincePath, data => {
        let dataArr = buildData(data);
        addCities(dataArr, () => {
            let addCityCount = 0;
            dataArr.forEach((item, index) => {
                let pathArr = provincePath.split('/');
                pathArr[pathArr.length - 1] = item[0];
                let newPath = pathArr.join('/');
                setTimeout(() => {
                    queryCityHtml(newPath, newData => {
                        let newDataArr = buildData(newData);
                        addCities(newDataArr, () => {
                            addCityCount++;
                            if (addCityCount === dataArr.length) {
                                finishCallback();
                            }
                        }, err => {
                            failCallback(err);
                            // 插入县数据库失败
                            console.log('插入县数据库失败');
                            console.log(err);
                        })
                    })
                }, 200 * index)
            })
        }, err => {
            failCallback(err);
            // 插入城市数据库失败
            console.log('插入数据库失败');
            console.log(err);
        })
    }, err => {
        failCallback(err);
        // 查询html失败
        console.log('查询html失败')
        console.log(err)
    })
}

// 爬取国土统计局的统计数据
function queryCityHtml (path, successCallback, failCallback) {
    let baseUrl = 'http://www.stats.gov.cn/tjsj/tjbz/tjyqhdmhcxhfdm/2019'
    console.log(baseUrl + path);
    http.get(baseUrl + path, function (res) {
        let arrBuf = [];
        let bufLength = 0;
        res.on('data', function (chunk) {
            arrBuf.push(chunk);
            bufLength += chunk.length;
        }).on('end', function () {
            let chunkAll = Buffer.concat(arrBuf, bufLength);
            let resString = iconv.decode(chunkAll, 'gb2312');
            successCallback(resString);
        }).on('error', function (error) {
            failCallback(error)
        })
    });
}

// 新增城市
function addCities (dataArr, successCallback, failCallback) {
    console.log(dataArr)
    let sql = 'INSERT INTO `' + CITY_CODE + '` (code, name, level) VALUES '
    let valueStr = dataArr.map(item => `(${item[1]},"${item[2]}",${item[3]})`).join(',');
    dbController.dbConnect(sql + valueStr, null, (err, rows) => {
        if (err) {
            failCallback(err);
        } else {
            successCallback();
        }
    })
}

//解析国土统计局的html文件获得cityCode跟cityName
function buildData (string) {
    let level;
    let splitString = "<tr class='citytr'>";
    if (string.indexOf('citytr') > 0) {
        level = 2;
    } else if (string.indexOf('countytr') > 0) {
        level = 3;
        splitString = "<tr class='countytr'>";
    } else {
        level = 3;
        splitString = "<tr class='towntr'>";
    }
    let start = string.indexOf(splitString)
    let end = string.indexOf('</table>')
    const html = string.substring(start, end);
    let htmlArr = html.split(splitString);
    htmlArr.splice(0, 1);
    htmlArr = htmlArr.map(item => {
        let arr = getItemValue(item);
        arr.push(level);
        return arr;
    });
    return htmlArr;
    function getItemValue (value) {
        // value
        // <td><a href='13/1301.html'>130100000000</a></td><td><a href='13/1301.html'>ʯ��ׯ��</a></td></tr>
        // <td>350501000000</td><td>市辖区</td></tr>
        let linkType = false;
        let resultArr = [];
        if (value.indexOf('.html') > 0) {
            linkType = true;
        }
        let arr = value.split('</td><td>');
        if (linkType) {
            resultArr[0] = arr[0].substring(arr[0].indexOf("href='") + "href='".length, arr[0].indexOf("'>"));
            resultArr[1] = arr[0].substring(arr[0].indexOf("'>") + 2, arr[0].indexOf("</a>"));
            resultArr[2] = arr[1].substring(arr[1].indexOf("'>") + 2, arr[1].indexOf("</a>"));
        } else {
            resultArr[0] = '';
            resultArr[1] = arr[0].substr('<td>'.length);
            resultArr[2] = arr[1].substring(0, arr[1].indexOf('</td>'));
        }
        return resultArr;
    }
}

// 查询全部的城市
function queryAllCities () {
    return new Promise(function (resolve, reject) {
        dbController.query(CITY_CODE, null, null, (err, rows) => {
            if (err) {
                reject(err)
            } else {
                resolve(rows.map(item => resFilter(item)))
            }
        })
    })
}

module.exports = {
    addChinaCity,
    queryAllCities
}