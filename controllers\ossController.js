const fsp = require('fs').promises
const fs = require('fs')
const path = require('path')
const axios = require('axios')
const https = require('https')
const { addFileToBucket } = require('../utils/aliyunOs')
const { format } = require('../utils/date')

// 建议: 将这些值移动到专门的配置文件 (例如 config.js 或 .env) 中
const OSS_HOST = 'https://qwc-prd.oss-cn-hangzhou.aliyuncs.com'
const TEMP_DIR = path.join(__dirname, '../temp')

/**
 * 从 URL 下载文件到本地临时目录。
 * @param {string} url - 要下载的文件的 URL。
 * @returns {Promise<string>} 返回临时文件路径的 Promise。
 * @private
 */
async function _downloadFileToTemp(url) {
	// 确保临时目录存在
	await fsp.mkdir(TEMP_DIR, { recursive: true })
	// 忽略 SSL 证书验证
	const httpsAgent = new https.Agent({
		rejectUnauthorized: false
	})

	const response = await axios({
		url,
		method: 'GET',
		responseType: 'stream',
		httpsAgent,
		headers: {
			Accept: '*/*'
		}
	})

	const fileName = path.basename(new URL(url).pathname) || `temp-download-${Date.now()}`
	const tempFilePath = path.join(TEMP_DIR, fileName)
	const writer = fs.createWriteStream(tempFilePath)

	response.data.pipe(writer)

	return new Promise((resolve, reject) => {
		writer.on('finish', () => resolve(tempFilePath))
		writer.on('error', err => {
			// 确保在出错时关闭写入器并删除部分下载的文件
			writer.close()
			fsp
				.unlink(tempFilePath)
				.catch(unlinkErr => console.error(`清理部分下载失败: ${tempFilePath}`, unlinkErr))
			reject(err)
		})
	})
}

/**
 * 使用流高效地将本地文件上传到 OSS。
 * @param {string} localPath - 本地文件的完整路径。
 * @param {string} [namespace='common'] - OSS 存储桶中的子目录（命名空间）。
 * @returns {Promise<string>} 返回完整 OSS URL 的 Promise。
 * @private
 */
async function _uploadLocalFile(localPath, namespace = 'common') {
	try {
		const stats = await fsp.stat(localPath)
		if (!stats.isFile()) {
			throw new Error(`提供的路径不是一个文件: ${localPath}`)
		}

		const fileName = path.basename(localPath)
		const datePrefix = format(new Date(), 'yyyyMMdd')
		const ossPath = path.posix.join('/', namespace, datePrefix, fileName)

		// 为保证上传成功，使用通用的 MIME 类型。
		// 如需更精确的类型，可引入 'mime-types' 等库。
		const contentType = 'application/octet-stream'
		const fileSize = stats.size

		const fileStream = fs.createReadStream(localPath)

		try {
			// 调用已更新的 uploader，传入流、路径、大小和内容类型
			await addFileToBucket(fileStream, ossPath, fileSize, contentType)
			return `${OSS_HOST}${ossPath}`
		} catch (error) {
			// 确保在上传失败时销毁流
			fileStream.destroy()
			throw new Error(`上传文件流到 OSS 失败: ${error.message}`)
		}
	} catch (error) {
		throw new Error(`处理本地文件失败 ${localPath}: ${error.message}`)
	}
}

/**
 * 将文件上传到 OSS。源可以是本地文件路径或远程 URL。
 * 这是上传单个文件的主要函数。
 *
 * @param {string} source - 本地文件路径或远程 URL。
 * @param {string} [namespace='common'] - OSS 存储桶中的子目录（命名空间）。
 * @returns {Promise<string>} 返回上传文件的完整 OSS URL 的 Promise。
 */
async function uploadToOSS(source, namespace = 'common') {
	const isRemoteFile = /^https?:\/\//.test(source)
	let localPath = source
	let shouldCleanup = false

	try {
		if (isRemoteFile) {
			localPath = await _downloadFileToTemp(source)
			shouldCleanup = true // 仅当文件是下载的时才标记为需要清理
		}

		return await _uploadLocalFile(localPath, namespace)
	} catch (error) {
		throw new Error(`OSS 上传失败，源: "${source}": ${error.message}`)
	} finally {
		if (shouldCleanup) {
			await fsp.unlink(localPath).catch(err => {
				console.error(`清理临时文件失败: ${localPath}`, err)
			})
		}
	}
}

/**
 * 并行将一组 URL 文件上传到 OSS。
 *
 * @param {string[]} urlArray - 要上传的 URL 数组。
 * @param {string} [namespace='common'] - 所有文件的通用子目录。
 * @returns {Promise<string[]>} 返回一个包含 OSS URL 数组的 Promise。
 */
function batchAddUrlFileToOss(urlArray, namespace = 'common') {
	if (!Array.isArray(urlArray)) {
		return Promise.reject(new TypeError('输入必须是 URL 数组。'))
	}
	const uploadPromises = urlArray.map(url => uploadToOSS(url, namespace))
	return Promise.all(uploadPromises)
}

module.exports = {
	uploadToOSS,
	batchAddUrlFileToOss
}
