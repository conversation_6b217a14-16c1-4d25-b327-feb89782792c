const { USER, ADMIN, LOG } = require('../db').tableNames
const { valueToEnumKey, USER_ROLE } = require('../constant/enum')
const { resFilter, getKeysObj } = require('../utils/filter')
const dbController = require('./dbController')
const wxCommonApi = require('../utils/wxCommonApi')
const mpCommonApi = require('../utils/mpCommonApi')
const gps = require('../utils/gps')
const { NZ_MINI, NZ_SERVICE } = require('../constant/appName')
const userSql = require('../sql/user')
const userUpdateKeys = [
	'mobile',
	'latitude',
	'longitude',
	'cityCode',
	'address',
	'wxMiniOpenId',
	'wxOfficialOpenId',
	'name',
	'avatar',
	'gender',
	'type',
	'isPaid',
	'images',
	'saleProducts',
	'officialSubscribe',
	'points',
	'purchaseCrops',
	'transportCars',
	'description',
	'contactedCount'
]
// const userCreateKeys = ['wxUnionId'].concat(userUpdateKeys);
const wxInfoKeys = [
	'wxUnionId',
	'wxMiniOpenId',
	'wxOfficialOpenId',
	'name',
	'avatar',
	'gender',
	'officialSubscribe'
]

function getMiniWxUser(code, encryptedData, iv) {
	return wxCommonApi.requestWxEncryptedData(NZ_MINI, code, encryptedData, iv)
}

function getMiniWxPhone(code) {
	return wxCommonApi.requestWxPhone(NZ_MINI, code)
}

function getOfficialWxUser(code, encryptedData, iv) {
	return wxCommonApi.requestWxEncryptedData(NZ_SERVICE, code, encryptedData, iv)
}

function getUserByMiniInfo(queryOption) {
	return new Promise(function (resolve, reject) {
		dbController.query(USER, queryOption, null, (err, rows) => {
			if (err) {
				reject(err)
			} else if (rows[0]) {
				resolve(resFilter(rows[0]))
			} else {
				resolve(null)
			}
		})
	})
}

// 微信登录
function wxLogin(reqBody) {
	return new Promise((resolve, reject) => {
		const wxInfo = {}
		wxInfoKeys.forEach(key => {
			if (reqBody[key]) {
				wxInfo[key] = reqBody[key]
			}
		})
		dbController.query(USER, { wxUnionId: wxInfo.wxUnionId }, null, (err, rows) => {
			if (err) {
				reject(err)
			} else if (rows[0]) {
				resolve(resFilter(rows[0]))
			} else {
				dbController.add(USER, wxInfo, (err, res) => {
					if (err) {
						reject(err)
					} else {
						let id = res.insertId
						dbController.query(USER, { id }, null, (err, rows) => {
							if (err) {
								reject(err)
							} else {
								resolve(resFilter(rows[0]))
							}
						})
					}
				})
			}
		})
	})
}

function getMiniUserByCode(code, platform) {
	if (platform === 'mp-weixin') {
		return mpCommonApi.getQwcWxMiniSessionData(code)
	} else if (platform === 'mp-toutiao') {
		return mpCommonApi.getDyMiniSessionData(code)
	} else if (platform === 'mp-kuaishou') {
		return mpCommonApi.getKsMiniSessionData(code)
	}
	return Promise.reject('参数有误')
}

async function queryOrAddMiniUser(miniUser, inviteUserId, platform) {
	try {
		const { wxUnionId, dyUnionId, dyMiniOpenId, ksMiniOpenId, ksUnionId } = miniUser
		let user = null
		let canCompleteInfo = {} // 可以给用户补全的信息
		if (platform === 'mp-weixin') {
			user = await getUserByMiniInfo({ wxUnionId })
			if (user) {
				;['wxMiniOpenId', 'wxOfficialOpenId'].forEach(key => {
					if (user[key] != miniUser[key] && !miniUser[key]) {
						canCompleteInfo[key] = miniUser[key]
					}
				})
			}
		} else if (platform === 'mp-toutiao') {
			user = await getUserByMiniInfo({ dyMiniOpenId })
			if (user && user.dyUnionId != dyUnionId && !dyUnionId) {
				canCompleteInfo.dyUnionId = dyUnionId
			}
		} else if (platform === 'mp-kuaishou') {
			user = await getUserByMiniInfo({ ksMiniOpenId })
			if (user && user.ksUnionId != ksUnionId && ksUnionId) {
				canCompleteInfo.ksUnionId = ksUnionId
			}
		}
		if (user) {
			// 用户已存在，检查用户有没有追加更多的信息，若是有，先保存用户新增信息
			if (Object.keys(canCompleteInfo).length > 0) {
				user = await getUserAfterUpdate(user.id, canCompleteInfo)
			}
		} else {
			const newUser = {}
			const keys = [
				'wxUnionId',
				'wxMiniOpenId',
				'wxOfficialOpenId',
				'dyUnionId',
				'dyMiniOpenId',
				'ksMiniOpenId',
				'ksUnionId',
				'inviteUserId'
			]
			keys.forEach(key => {
				if (miniUser[key]) {
					newUser[key] = miniUser[key]
				}
			})
			newUser.inviteUserId = inviteUserId
			newUser.points = 3 // 新用户初始化默认3积分
			user = await getUserAfterAdd(newUser)
		}
		return Promise.resolve(user)
	} catch (error) {
		return Promise.reject(error)
	}
}

function getUserAfterAdd(newUser) {
	return new Promise(function (resolve, reject) {
		dbController.add(USER, newUser, (err, res) => {
			if (err) {
				reject(err)
			} else {
				dbController.query(USER, { id: res.insertId }, null, (err, rows) => {
					if (err) {
						reject(err)
					} else {
						resolve(resFilter(rows[0]))
					}
				})
			}
		})
	})
}

function getUserAfterUpdate(id, updateInfo) {
	return new Promise(function (resolve, reject) {
		dbController.update(USER, { id }, updateInfo, (err, rows) => {
			if (err) {
				reject(err)
			} else {
				dbController.query(USER, { id }, null, (err, rows) => {
					if (err) {
						reject(err)
					} else {
						resolve(resFilter(rows[0]))
					}
				})
			}
		})
	})
}

// 查询用户的个人行为动作的统计次数
function getMyLogCount(userId, key) {
	return new Promise((resolve, reject) => {
		dbController.count(LOG, { userId, key }, (err, rows) => {
			if (err) {
				reject(err)
			} else {
				const total = rows[0]['count(*)']
				resolve({ total })
			}
		})
	})
}

// 查询用户的日志行为，以及上一次的日志记录
function getMyLogCountAndLatestLog(userId, key) {
	return new Promise((resolve, reject) => {
		dbController.count(LOG, { userId, key }, (err, rows) => {
			if (err) {
				reject(err)
			} else {
				const total = rows[0]['count(*)']
				if (total > 0) {
					dbController.query(
						LOG,
						{ userId, key },
						{ pageIndex: 0, pageSize: 1, orderBy: 'id' },
						(err, rows) => {
							if (err) {
								reject(err)
							} else {
								resolve({ total, latestRecord: resFilter(rows[0]) })
							}
						}
					)
				} else {
					resolve({ total, latestRecord: null })
				}
			}
		})
	})
}

function mobileLogin(mobile, password) {
	return new Promise((resolve, reject) => {
		dbController.query(USER, { mobile, password }, null, (err, rows) => {
			if (err) {
				reject(err)
			} else if (rows[0]) {
				resolve(resFilter(rows[0]))
			} else {
				reject('账号或密码有误')
			}
		})
	})
}

function updateInfo(id, reqBody) {
	return new Promise((resolve, reject) => {
		const updateData = getKeysObj(reqBody, userUpdateKeys)
		dbController.update(USER, { id }, updateData, (err, rows) => {
			if (err) {
				reject(err)
			} else {
				resolve('更新成功')
			}
		})
	})
}

function getUserInfo(id) {
	return new Promise((resolve, reject) => {
		dbController.query(USER, { id }, null, (err, rows) => {
			if (err) {
				reject(err)
			} else if (rows.length > 0) {
				delete rows[0].password
				resolve(resFilter(rows[0]))
			} else {
				reject('账号不存在')
			}
		})
	})
}

function queryUsers(mobile, name, address, type, pageIndex, pageSize) {
	const queryOptions = {
		'mobile.like': mobile,
		'name.like': name,
		'address.like': address,
		type
	}
	return new Promise((resolve, reject) => {
		dbController.count(USER, queryOptions, (err, rows) => {
			if (err) {
				reject(err)
			} else {
				const total = rows[0]['count(*)']
				dbController.query(USER, queryOptions, { pageIndex, pageSize }, (err, rows) => {
					if (err) {
						reject(err)
					} else {
						resolve({
							total,
							list: rows.map(item => {
								delete item.password
								return resFilter(item)
							})
						})
					}
				})
			}
		})
	})
}

function queryAllAdmins() {
	return new Promise((resolve, reject) => {
		dbController.query(ADMIN, null, null, (err, rows) => {
			if (err) {
				reject(err)
			} else {
				resolve(
					rows.map(item => {
						item.type = valueToEnumKey(USER_ROLE, item.type)
						return resFilter(item)
					})
				)
			}
		})
	})
}

function getMyRoles(userId) {
	return new Promise((resolve, reject) => {
		dbController.query(ADMIN, { userId, status: 1 }, null, (err, rows) => {
			if (err) {
				reject(err)
			} else {
				resolve(
					rows.map(item => {
						item.type = valueToEnumKey(USER_ROLE, item.type)
						return resFilter(item)
					})
				)
			}
		})
	})
}

module.exports = {
	getMiniWxUser,
	getMiniWxPhone,
	getOfficialWxUser,
	getMiniUserByCode,
	queryOrAddMiniUser,
	wxLogin,
	mobileLogin,
	updateInfo,
	getUserInfo,
	queryUsers,
	queryAllAdmins,
	getMyRoles,
	getMyLogCount,
	getMyLogCountAndLatestLog
}
