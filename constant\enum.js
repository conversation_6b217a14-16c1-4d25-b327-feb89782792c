const ORDER_STATUS = {
	default: 0, // 求购中
	finish: 1, // 已交易
	stop: 2, // 已暂停
	cancel: 3 // 已取消
}

const ORDER_INFO_STATUS = {
	default: 0, // 默认状态
	ask: 1, // 请求测量
	finish: 2 // 已上传测量数据
}

const ORDER_RELATION_TYPE = {
	like: 0, // 收藏
	ask: 1, // 请求测量
	telephone: 2, // 打电话
	message: 3, // 发信息留言
	view: 4 // 浏览
}

const USER_TYPE = {
	farmer: 0,
	buyer: 1,
	collector: 10,
	admin: 100
}

// 给 t_admin 表服务
const USER_ROLE = {
	superAdmin: 0,
	admin: 1,
	operator: 2, // 运营人员
	collector: 3 // 信息收集员
}

// 各平台渠道
const PLATFORM = {
	weixin: 'mp-weixin',
	douyin: 'mp-toutiao',
	kuaishou: 'mp-kuaishou'
}

const INFO_SOURCE = {
	ChineseAgriculturalSupplyAndDemandEstimates: 1, // 中国农产品供需形势分析
	NationalGrainTradeCenter: 2, // 国家粮食交易中心
	ChinaSoybean: 3, // 中国大豆产业协会
	ChinaCotton: 4, // 中国棉花协会
	SDLscb: 5, // 山东省粮食和物资储备局
	HBLswz: 6, // 河北省粮食和物资储备局
	JLLswz: 7, // 吉林省粮食和物资储备局
	XJLswz: 8, // 新疆维吾尔自治区粮食和物资储备局
	NMGLswz: 9 // 内蒙古自治区粮食和物资储备局
}

function valueToEnumKey(enumObj, value) {
	for (const key in enumObj) {
		if (enumObj[key] === value) {
			return key
		}
	}
	return null
}

module.exports = {
	valueToEnumKey,
	ORDER_STATUS,
	ORDER_INFO_STATUS,
	ORDER_RELATION_TYPE,
	USER_TYPE,
	USER_ROLE,
	PLATFORM,
	INFO_SOURCE
}
