const createError = require('http-errors')
const express = require('express')
const path = require('path')
const cookieParser = require('cookie-parser')
const cookieSession = require('cookie-session')

const logger = require('morgan')

const indexRouter = require('./routes/index')
// const emailRouter = require('./routes/email');
const userRouter = require('./routes/users')
const orderRouter = require('./routes/order')
const commonRouter = require('./routes/common')
const logRouter = require('./routes/log')
const messageRouter = require('./routes/message')
const priceRouter = require('./routes/price')
const infoRouter = require('./routes/info')
const pointsRouter = require('./routes/points')
const traderRouter = require('./routes/traders')
const transportRouter = require('./routes/transport')
const adminRouter = require('./routes/admin/index')

const resCommon = require('./middleware/resCommon')
const adminAuth = require('./middleware/admin')

const app = express()

// view engine setup
app.set('views', path.join(__dirname, 'views'))
app.set('view engine', 'jade')

app.use(logger('dev'))
app.use(express.json())
app.use(express.urlencoded({ extended: false }))
app.use(cookieParser())
app.use(
	cookieSession({
		name: 'qwc_session',
		keys: ['iuertnffd(*)@#soigs!@klajkl@#%$#asdfa'],
		maxAge: 1000 * 60 * 60 * 2 //设置过期时间，2小时后过期
	})
)
app.use(express.static(path.join(__dirname, 'public')))

app.use('/', indexRouter)
app.use('/api/v1/admin', adminAuth)

// 给下面的api请求增加res统一返回
app.use(resCommon)
// app.use('/api/v1/email', emailRouter);
app.use('/api/v1/user', userRouter)
app.use('/api/v1/order', orderRouter)
app.use('/api/v1/common', commonRouter)
app.use('/api/v1/log', logRouter)
app.use('/api/v1/message', messageRouter)
app.use('/api/v1/price', priceRouter)
app.use('/api/v1/info', infoRouter)
app.use('/api/v1/points', pointsRouter)
app.use('/api/v1/trader', traderRouter)
app.use('/api/v1/transport', transportRouter)
app.use('/api/v1/admin', adminRouter)

// catch 404 and forward to error handler
app.use(function (req, res, next) {
	next(createError(404))
})

// error handler
app.use(function (err, req, res, next) {
	// set locals, only providing error in development
	res.locals.message = err.message
	res.locals.error = req.app.get('env') === 'development' ? err : {}

	// render the error page
	res.status(err.status || 500)
	res.render('error')
})

module.exports = app
