import http from '@/utils/http'

/**
 * 微信群二维码管理器
 * 提供二维码的增删改查功能
 */

/**
 * 查询二维码列表
 * @param {string} userName - 用户名（可选）
 * @param {string} userMobile - 用户手机号（可选）
 * @param {number} isActive - 是否生效（可选，1:生效, 0:不生效）
 * @param {number} pageIndex - 页码
 * @param {number} pageSize - 每页数量
 * @returns {Promise} 返回二维码列表数据
 */
function queryQrcodes(userName, userMobile, isActive, pageIndex, pageSize) {
	return http.post('admin/qrcode/load', {
		userName,
		userMobile,
		isActive,
		pageIndex,
		pageSize
	})
}

/**
 * 创建新的二维码
 * @param {Object} qrcodeData - 二维码数据
 * @param {number} qrcodeData.userId - 用户ID（可选，为空表示系统级二维码）
 * @param {string} qrcodeData.qrCodeUrl - 二维码图片URL
 * @param {string} qrcodeData.description - 描述信息
 * @param {number} qrcodeData.isActive - 是否生效（1:生效, 0:不生效）
 * @returns {Promise} 返回创建结果
 */
function createQrcode(qrcodeData) {
	return http.post('admin/qrcode/create', qrcodeData)
}

/**
 * 更新二维码信息
 * @param {number} id - 二维码ID
 * @param {Object} updateData - 更新数据
 * @param {string} updateData.qrCodeUrl - 二维码图片URL（可选）
 * @param {string} updateData.description - 描述信息（可选）
 * @param {number} updateData.isActive - 是否生效（可选）
 * @returns {Promise} 返回更新结果
 */
function updateQrcode(id, updateData) {
	return http.put('admin/qrcode/update/' + id, updateData)
}

/**
 * 删除二维码
 * @param {number} id - 二维码ID
 * @returns {Promise} 返回删除结果
 */
function deleteQrcode(id) {
	return http.delete('admin/qrcode/delete/' + id)
}

/**
 * 获取二维码详情
 * @param {number} id - 二维码ID
 * @returns {Promise} 返回二维码详情
 */
function getQrcodeDetail(id) {
	return http.get('admin/qrcode/detail/' + id)
}

/**
 * 上传二维码图片
 * @param {File} file - 图片文件
 * @returns {Promise} 返回上传结果，包含图片URL
 */
function uploadQrcodeImage(file) {
	const formData = new FormData()
	formData.append('file', file)
	// 直接使用axios进行文件上传，绕过http工具的qs.stringify处理
	return import('axios').then(axios => {
		return axios.default
			.post('/api/v1/admin/qrcode/upload', formData, {
				headers: {
					'Content-Type': 'multipart/form-data'
				}
			})
			.then(res => {
				if (res.data.code === 1) {
					return res.data.data
				} else {
					throw new Error(res.data.message || '上传失败')
				}
			})
	})
}

/**
 * 获取所有用户列表（用于下拉选择）
 * @returns {Promise} 返回用户列表
 */
function getAllUsers() {
	return http.get('admin/qrcode/users')
}

/**
 * 批量更新二维码状态
 * @param {Array} ids - 二维码ID数组
 * @param {number} isActive - 是否生效（1:生效, 0:不生效）
 * @returns {Promise} 返回批量更新结果
 */
function batchUpdateQrcodeStatus(ids, isActive) {
	return http.post('admin/qrcode/batch-status', {
		ids,
		isActive
	})
}

export default {
	queryQrcodes,
	createQrcode,
	updateQrcode,
	deleteQrcode,
	getQrcodeDetail,
	uploadQrcodeImage,
	getAllUsers,
	batchUpdateQrcodeStatus
}
