# 实施计划

- [x] 1. 创建新项目目录并复制源代码



  - 创建 `admin-vue3` 新目录作为迁移项目根目录
  - 复制当前项目的所有源代码文件到新目录
  - 复制配置文件（除了需要替换的构建配置）
  - 确保原项目目录完全不受影响
  - _需求: 保持原项目完整性_

- [x] 2. 设置基础依赖和包管理


  - 在新目录下更新 package.json，添加 Vue 3、Element Plus、Vite 相关依赖
  - 移除 Vue CLI 依赖，添加 Vite 构建系统
  - 更新开发依赖（ESLint、测试工具）
  - _需求: 1.1, 3.1, 5.1_

- [x] 3. 配置 Vite 构建系统


  - 在新项目中创建 vite.config.js，配置 Vue 3 插件
  - 配置开发服务器代理设置
  - 设置环境变量处理（VUE_APP_ → VITE_）
  - 更新 package.json 脚本命令使用 Vite
  - _需求: 3.2, 3.3, 3.5_

- [x] 4. 更新 ESLint 配置支持 Vue 3


  - 修改 .eslintrc.js 支持 Vue 3 和 Composition API
  - 添加 script setup 语法的 ESLint 规则
  - 配置现代 JavaScript 特性的代码检查
  - _需求: 5.1, 5.2_

- [x] 5. 迁移主应用入口文件


  - 将 src/main.js 从 Vue 2 转换为 Vue 3 createApp 语法
  - 更新 Element UI 导入为 Element Plus
  - 迁移全局插件注册方式
  - 保留现有的 Promise.all 初始化逻辑
  - _需求: 1.1, 2.1, 6.1_



- [ ] 6. 升级路由配置
  - 更新 src/router/index.js 为 Vue Router 4 语法
  - 替换 VueRouter 构造函数为 createRouter 和 createWebHistory
  - 保持所有现有路由和元信息
  - 如需要则更新路由守卫语法

  - _需求: 4.1, 4.2, 6.3_

- [x] 7. 迁移状态管理到 Pinia

  - 安装 Pinia 并创建 store 配置
  - 将 Vuex store 转换为 Pinia store 结构
  - 创建 useAuthStore 管理认证状态
  - 创建 useAppStore 管理应用状态（作物、区域、菜单）
  - 保持所有现有状态属性，将 mutations 转换为 actions
  - _需求: 4.3, 6.4_

- [x] 8. 转换 App.vue 为 Composition API


  - 将 App.vue 从 Options API 重构为 script setup
  - 更新模板使用新的 store 语法
  - 转换生命周期钩子（created → onMounted）
  - 更新组件导入和注册方式
  - _需求: 1.2, 1.3, 6.5_

- [ ] 9. 迁移布局组件到 Vue 3
- [x] 9.1 转换 Sidebar 组件


  - 将 src/views/Sidebar.vue 重构为 Composition API
  - 更新 Element UI 组件为 Element Plus
  - 转换 store 访问为 Pinia composables
  - 更新图标使用为 Element Plus 图标
  - _需求: 1.2, 2.2, 2.5_

- [x] 9.2 转换 Navbar 组件


  - 将 src/views/Navbar.vue 重构为 Composition API
  - 更新 Element UI 组件为 Element Plus
  - 转换 store 访问为 Pinia composables
  - 更新消息/通知使用为 Element Plus API
  - _需求: 1.2, 2.3, 2.4_

- [ ] 9.3 转换 Login 组件



  - 将 src/views/Login.vue 重构为 Composition API
  - 更新表单验证为 Element Plus 语法
  - 转换认证逻辑使用 Pinia store
  - 更新消息处理为 ElMessage
  - _需求: 1.2, 2.3, 7.3_

- [ ] 10. 迁移业务视图组件
- [ ] 10.1 转换订单管理组件
  - 将 src/views/order/List.vue 重构为 Composition API
  - 更新 Element UI 表格、表单、对话框组件为 Element Plus
  - 转换 API 调用使用现代 async/await 模式
  - 更新 store 访问为 Pinia composables
  - _需求: 1.2, 2.2, 7.1_

- [ ] 10.2 转换价格管理组件
  - 将 src/views/price/*.vue 组件重构为 Composition API
  - 更新 Element UI 组件为 Element Plus 等价组件
  - 转换表单验证和数据处理
  - 更新路由和导航逻辑
  - _需求: 1.2, 2.2, 7.1_

- [ ] 10.3 转换系统管理组件
  - 将 src/views/system/*.vue 组件重构为 Composition API
  - 更新 Element UI 组件为 Element Plus
  - 转换 CRUD 操作和表单处理
  - 更新数据验证和错误处理
  - _需求: 1.2, 2.2, 7.1_

- [ ] 10.4 转换其余视图组件
  - 重构 src/views/user/List.vue、src/views/info/List.vue 等
  - 更新所有 Element UI 组件为 Element Plus
  - 转换组件逻辑为 Composition API
  - 更新 store 和 router 使用方式
  - _需求: 1.2, 2.2, 7.1_

- [ ] 11. 更新管理器模块以兼容 Vue 3
- [ ] 11.1 现代化 API 管理器
  - 使用现代异步模式更新 src/manager/companyManager.js
  - 改进 src/manager/orderManager.js 的错误处理
  - 更新 src/manager/contextManager.js 以兼容 Vue 3
  - 添加适当的 TypeScript 风格 JSDoc 注释
  - _需求: 6.2, 7.2_

- [ ] 11.2 更新工具模块
  - 更新 src/utils/date.js 以兼容 Vue 3
  - 移除 Vue.prototype 扩展，使用 app.config.globalProperties
  - 更新其他工具函数以兼容 Vue 3
  - _需求: 1.6, 6.2_

- [ ] 12. 实现 Element Plus 图标系统
  - 安装 @element-plus/icons-vue 包
  - 创建图标注册工具
  - 将所有图标使用从字体图标更新为 SVG 图标
  - 更新侧边栏和导航图标
  - _需求: 2.5_

- [ ] 13. 更新样式和主题系统
  - 更新 Element Plus CSS 导入
  - 确保 SCSS 编译在 Vite 中正常工作
  - 更新任何 Element UI 特定样式为 Element Plus
  - 保持现有自定义样式和主题
  - _需求: 2.6_

- [ ] 14. 实现暗黑模式支持
  - 添加暗黑模式切换功能
  - 配置 Element Plus 主题切换
  - 更新自定义样式以兼容暗黑模式
  - 添加用户偏好持久化
  - _需求: 8.2_

- [ ] 15. 增强响应式设计和移动端支持
  - 更新布局组件以获得更好的移动端体验
  - 改进移动端侧边栏折叠行为
  - 更新表格和表单布局适应小屏幕
  - 测试并修复任何响应式设计问题
  - _需求: 8.3_

- [ ] 16. 实现改进的加载状态
  - 为所有异步操作添加加载指示器
  - 为数据表格实现骨架屏加载
  - 为表单提交添加进度指示器
  - 更新错误处理以提供更好的用户反馈
  - _需求: 8.4_

- [ ] 17. 优化性能和包大小
  - 配置 Vite 以获得最佳生产构建
  - 实现基于路由的代码分割
  - 优化 Element Plus 导入（tree shaking）
  - 分析并优化包大小
  - _需求: 9.3, 9.4_

- [ ] 18. 创建综合测试套件
  - 为 Vue 3 设置 Vue Test Utils
  - 为转换的组件创建单元测试
  - 测试 Pinia store 功能
  - 测试路由导航和守卫
  - 验证所有现有功能正常工作
  - _需求: 7.1, 7.2, 7.3, 7.4, 7.5_

- [ ] 19. 性能基准测试和验证
  - 测量 Vite 带来的构建时间改进
  - 基准测试运行时性能改进
  - 验证包大小优化
  - 测试加载时间改进
  - _需求: 9.1, 9.2, 9.3, 9.4_

- [ ] 20. 最终集成测试和错误修复
  - 测试完整应用程序功能
  - 验证认证和授权
  - 测试所有 CRUD 操作和 API 交互
  - 修复任何剩余的兼容性问题
  - 确保控制台没有错误和警告
  - _需求: 7.1, 7.2, 7.3, 7.4, 7.5, 10.5_

- [ ] 21. 文档和部署准备
  - 创建包含前后对比示例的迁移文档
  - 记录使用 Vite 的新开发工作流程
  - 更新新构建系统的部署脚本
  - 创建回滚程序和验证检查清单
  - _需求: 10.1, 10.2, 10.4_