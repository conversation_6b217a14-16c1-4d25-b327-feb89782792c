<template>
    <!-- Main Sidebar Container -->
    <aside class="main-sidebar sidebar-dark-primary elevation-4">
        <!-- Brand Logo -->
        <a href="index.html" class="brand-link">
            <img src="../../images/logo.png" alt="Logo" class="brand-image img-circle elevation-3" style="opacity: .8">
            <span class="brand-text font-weight-light">真农易管理后台</span>
        </a>

        <!-- Sidebar -->
        <div class="sidebar">
            <!-- Sidebar Menu -->
            <nav class="mt-2">
                <ul class="nav nav-pills nav-sidebar flex-column" data-animation-speed="400" data-widget="treeview" data-accordion="true" role="menu">
                    <li class="nav-item">
                        <router-link to="/home/<USER>" class="nav-link">
                            <i class="nav-icon fa fa-chart-line"></i>
                            <p>
                                运营统计
                            </p>
                        </router-link>
                    </li>
                    <NavItem v-for="(router, index) in menus" :key="index" :router="router" />
                </ul>
            </nav>
            <!-- /.sidebar-menu -->
        </div>
        <!-- /.sidebar -->
    </aside>
</template>


<script>

import NavItem from '@/components/NavItem'

export default {
    components: { NavItem },
    data(){
        return {
            menus: []
        }
    },
    created(){
        let arr = [];
        this.$router.options.routes.forEach(router => {
            if(router.meta && router.meta.showInMenu){
                arr.push(router)
            }
        });
        this.menus = arr;
	},
	mounted(){
        // 对于路由跳转过来的场景
        // lte左测菜单挂载晚于 window.onload事件
        // 这里需要重新触发左侧菜单的事件绑定
        // 详细可查看adminlte.js源码 849行-853行
        $(window).trigger('load.lte.treeview')
	}
}
</script>