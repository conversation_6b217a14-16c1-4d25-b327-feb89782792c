import bootbox from 'bootbox';
// 对bootbox插件做一些封装

function alert(message, btn){
    return new Promise(function(resolve){
        bootbox.dialog({
            title: '提示',
            message,
            closeButton: false,
            buttons: {
                confirm: {
                    label: btn || '确定',
                    className: 'btn-success',
                    callback(){
                        resolve()
                    }
                }
            }
        })
    })
}

function confirm(message, btnArr){
    return new Promise(function(resolve, reject){
        let confirmLabel = '确定';
        let cancelLabel = '取消';
        if(btnArr){
            confirmLabel = btnArr[0] || confirmLabel;
            cancelLabel = btnArr[1] || cancelLabel;
        }
        bootbox.dialog({
            title: '提示',
            message,
            closeButton: false,
            buttons: {
                cancel: {
                    label: cancelLabel,
                    callback(){
                        reject()
                    }
                },
                confirm: {
                    label: confirmLabel,
                    className: 'btn-info',
                    callback(){
                        resolve()
                    }
                }
            }
        })
    })
}

function toast(message, duration, type){
    const toastTheme = {
        success: {
            color: '#67C23A',
            icon: 'fa fa-check-circle-o'
        },
        warning: {
            color: '#E6A23C',
            icon: 'fa fa-exclamation-circle'
        },
        error: {
            color: '#F56C6C',
            icon: 'fa fa-times-circle'
        }
    }
    duration = duration || 1500;
    if(type){
        message = `<p style="color:${toastTheme[type].color}" class="text-center mb-0"><i class="${toastTheme[type].icon}" aria-hidden="true"></i>&nbsp;&nbsp;${message}</p>`
    }else{
        message = `<p class="text-center mb-0">${message}</p>`;
    }
    const dialog = bootbox.dialog({
        message,
        closeButton: false
    });
    setTimeout(function(){
        dialog.modal('hide');
    }, duration);
}

['success', 'warning', 'error'].forEach(type => {
    toast[type] = function(message, duration){
        toast(message, duration, type)
    }
})

export default{
    alert,
    confirm,
    toast
}