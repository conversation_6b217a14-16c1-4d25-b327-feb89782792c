const express = require('express');
const router = express.Router();

//控制模块
const cropCompanyController = require('../../controllers/cropCompanyController');
const cropPortDailyController = require('../../controllers/cropPortDailyController');
const cropPriceController = require('../../controllers/cropPriceController');
const adminCropPricesController = require('../../controllers/adminCropPricesController');

router.post('/company/load', function (req, res) { // 查询收购厂家
    const { cityCode, name, pageIndex, pageSize } = req.body;
    adminCropPricesController.queryCropCompanies(cityCode, name, pageIndex, pageSize).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
})

router.post('/priceDaily/load', function (req, res) { //查询每日价格
    const { companyName, cropId, date, pageIndex, pageSize } = req.body;
    adminCropPricesController.queryCropPrices(companyName, cropId, date, pageIndex, pageSize).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
})

router.post('/priceDetail/load', function (req, res) { //查询企业价格标准
    const { companyId, cropId, pageIndex, pageSize } = req.body;
    cropCompanyController.queryCompanyDetails(companyId, cropId, pageIndex, pageSize).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
})

router.post('/addDetail', function (req, res) {//新增某个公司详情
    cropCompanyController.addCompanyDetail(req.body).then(data => {
        res.sendSuccess(data);
    }).catch(err => {
        res.sendMessage(err)
    })
})

router.put('/updateDetail/:id', function (req, res) {//更新企业标准详情
    const { id } = req.params;
    cropCompanyController.editCompanyDetail(id, req.body).then(data => {
        res.sendSuccess(data);
    }).catch(err => {
        res.sendMessage(err)
    })
})

router.get('/:companyId/detailLatest', function (req, res) {//查询某个公司价格标准详情
    let { companyId } = req.params;
    cropCompanyController.queryCompanyLatestDetail(companyId).then(data => {
        res.sendSuccess(data);
    }).catch(err => {
        res.sendMessage(err)
    })
})

router.post('/priceOther/load', function (req, res) { //查询港口数据
    const { date, pageIndex, pageSize } = req.body;
    adminCropPricesController.queryCropOthers(date, pageIndex, pageSize).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
})

router.post('/company/add', function (req, res) {//新增收购公司
    cropCompanyController.addCompany(req.body).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
})

router.put('/company/update/:id', function (req, res) {//更新收购公司
    const { id } = req.params;
    cropCompanyController.updateCompany(id, req.body).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
})

router.post('/price/add', function (req, res) {//新增收购价格
    const { cropId, companyId, companyName, price, unit, date } = req.body;
    cropPriceController.addPrice(cropId, companyId, companyName, price, unit, date).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
})

router.put('/price/update/:id', function (req, res) {//修改收购价格
    const { id } = req.params;
    const { cropId, companyId, price, date } = req.body;
    cropPriceController.updatePrice(id, cropId, companyId, price, date).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
})

router.post('/other/add', function (req, res) {//新增每日港口信息
    const { port1, port2, carCount, date } = req.body;
    cropPortDailyController.addDaily(port1, port2, carCount, date).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
})

router.put('/other/update/:id', function (req, res) {//更新其他信息
    const { id } = req.params;
    const { port1, port2, carCount, date } = req.body;
    cropPortDailyController.updateDaily(id, port1, port2, carCount, date).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
})

module.exports = router;