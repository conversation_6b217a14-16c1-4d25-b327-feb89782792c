import http from '@/utils/http';
import storeManager from '@/utils/storeManager'
const managerKey = 'qwc_cities_manager'

let cityArr = storeManager.get('cityArr', managerKey);

function queryAllCities () {
    return http.get('common/loadAllCities').then(areaArr => {
        const level1Areas = [];
        const level2Areas = [];
        const level3Areas = [];
        areaArr.forEach(area => {
            if (area.level === 1) {
                area.children = [];
                level1Areas.push(area);
            } else if (area.level === 2) {
                area.children = [];
                level2Areas.push(area);
            } else if (area.level === 3) {
                level3Areas.push(area);
            }
        });
        level3Areas.forEach(area => {
            const parentCode = area.code - area.code % 100000000;
            level2Areas.some(parent => {
                if (parent.code === parentCode) {
                    parent.children.push(area)
                    return true;
                }
            })
        })
        level2Areas.forEach(area => {
            const parentCode = area.code - area.code % 10000000000;
            level1Areas.some(parent => {
                if (parent.code === parentCode) {
                    parent.children.push(area)
                    return true;
                }
            })

        })
        storeManager.store('cityArr', managerKey, level1Areas, '30d');
        return level1Areas;
    })
}

function fetchAllCities () {
    return new Promise(function (resolve, reject) {
        if (cityArr) {
            resolve(cityArr)
        } else {
            queryAllCities().then(data => {
                resolve(data)
            }).catch(err => {
                reject(err)
            })
        }
    })
}

function queryCompanies (cityCode, cropId, name, pageIndex, pageSize) {
    return http.post('admin/priceCompany/company/load', { cityCode, cropId, name, pageIndex, pageSize })
}

function addCompany (cityCode, level, name) {
    return http.post('admin/priceCompany/company/add', { cityCode, level, name })
}

function updateCompany (id, cityCode, level, name) {
    return http.put('admin/priceCompany/company/update/' + id, { cityCode, level, name })
}

function queryCompanyLatestDetail (companyId) {
    return http.get(`admin/priceCompany/${companyId}/detailLatest`)
}

function queryCompanyDetails (companyId, cropId, pageIndex, pageSize) {
    return http.post('admin/priceCompany/priceDetail/load', { companyId, cropId, pageIndex, pageSize })
}

function addCompanyDetail (companyId, cropId, desc, photos) {
    return http.post('admin/priceCompany/addDetail', { companyId, cropId, desc, photos })
}

function updateCompanyDetail (id, companyId, cropId, desc, photos) {
    return http.put('admin/priceCompany/updateDetail/' + id, { companyId, cropId, desc, photos })
}

export default {
    queryAllCities,
    fetchAllCities,
    queryCompanies,
    addCompany,
    updateCompany,
    queryCompanyLatestDetail,
    queryCompanyDetails,
    addCompanyDetail,
    updateCompanyDetail
}