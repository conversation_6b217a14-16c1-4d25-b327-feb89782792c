<template>
	<div class="card">
		<!-- 搜索组件 -->
		<SearchForm v-model="searchParams" @search="loadData" @clear="handleClear" @add="handleAdd" />
		<div class="card-body">
			<!-- 表格组件 -->
			<QrcodeTable
				:loading="loading"
				:table-data="tableData"
				:total="total"
				:page-index.sync="pageIndex"
				:page-size.sync="pageSize"
				@toggle-status="handleToggleStatus"
				@edit="handleEdit"
				@delete="handleDelete"
				@pagination="loadData"
			/>
		</div>

		<!-- 新增弹窗组件 -->
		<AddDialog :visible.sync="addDialogVisible" @success="handleAddSuccess" />

		<!-- 编辑弹窗组件 -->
		<EditDialog
			:visible.sync="editDialogVisible"
			:edit-data="editData"
			@success="handleEditSuccess"
		/>
	</div>
</template>

<script>
	import qrcodeManager from '@/manager/qrcodeManager'
	import tableMixin from '@/mixins/tableMixin'

	// 导入拆分的组件
	import SearchForm from './components/SearchForm.vue'
	import QrcodeTable from './components/QrcodeTable.vue'
	import AddDialog from './components/AddDialog.vue'
	import EditDialog from './components/EditDialog.vue'

	export default {
		name: 'QrcodeList',
		components: {
			SearchForm,
			QrcodeTable,
			AddDialog,
			EditDialog
		},
		mixins: [tableMixin],
		data() {
			return {
				searchParams: {
					userName: '',
					userMobile: '',
					isActive: null
				},
				// 新增弹窗相关
				addDialogVisible: false,
				// 编辑弹窗相关
				editDialogVisible: false,
				editData: {}
			}
		},
		methods: {
			// 加载数据
			loadData() {
				const {
					searchParams: { userName, userMobile, isActive },
					pageIndex,
					pageSize
				} = this
				this.loading = true
				qrcodeManager
					.queryQrcodes(userName, userMobile, isActive, pageIndex, pageSize)
					.then(res => {
						const { total, list } = res
						this.total = total
						this.tableData = list
					})
					.catch(err => {
						this.$message.error(err)
					})
					.then(() => {
						this.loading = false
					})
			},

			// 清空搜索
			handleClear() {
				this.searchParams = {
					userName: '',
					userMobile: '',
					isActive: null
				}
				this.loadData()
			},
			// 新增
			handleAdd() {
				this.addDialogVisible = true
			},
			// 新增成功
			handleAddSuccess() {
				this.loadData()
			},
			// 编辑
			handleEdit(item) {
				this.editData = { ...item }
				this.editDialogVisible = true
			},
			// 编辑成功
			handleEditSuccess() {
				this.loadData()
			},
			// 切换状态
			handleToggleStatus(item) {
				const newStatus = item.isActive ? 0 : 1
				const action = newStatus ? '启用' : '禁用'

				this.$confirm(`确定要${action}该二维码吗？`, '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					qrcodeManager
						.updateQrcode(item.id, { isActive: newStatus })
						.then(() => {
							this.$message.success(`${action}成功`)
							this.loadData()
						})
						.catch(err => {
							this.$message.error(err)
						})
				})
			},
			// 删除
			handleDelete(item) {
				this.$confirm('确定要删除该二维码吗？删除后无法恢复！', '警告', {
					confirmButtonText: '确定删除',
					cancelButtonText: '取消',
					type: 'error'
				}).then(() => {
					qrcodeManager
						.deleteQrcode(item.id)
						.then(() => {
							this.$message.success('删除成功')
							this.loadData()
						})
						.catch(err => {
							this.$message.error(err)
						})
				})
			}
		},
		created() {
			// 页面初始化时不再需要加载所有用户
		}
	}
</script>

<style scoped>
	.card {
		padding: 20px;
	}

	.card-body {
		margin-top: 20px;
	}
</style>
