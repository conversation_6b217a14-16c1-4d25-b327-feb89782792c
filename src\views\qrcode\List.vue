<template>
	<div class="card">
		<!-- 搜索组件 -->
		<SearchForm v-model="searchParams" @search="loadData" @clear="handleClear" @add="handleAdd" />
		<div class="card-body">
			<!-- 表格组件 -->
			<QrcodeTable
				:loading="loading"
				:table-data="tableData"
				:total="total"
				:page-index.sync="pageIndex"
				:page-size.sync="pageSize"
				@toggle-status="handleToggleStatus"
				@edit="handleEdit"
				@delete="handleDelete"
				@pagination="loadData"
			/>
		</div>

		<!-- 二维码弹窗组件（新增/编辑合并） -->
		<QrcodeDialog :visible.sync="dialogVisible" :item="editData" @success="handleDialogSuccess" />
	</div>
</template>

<script>
	import qrcodeManager from '@/manager/qrcodeManager'
	import tableMixin from '@/mixins/tableMixin'

	import SearchForm from './components/SearchForm.vue'
	import QrcodeTable from './components/QrcodeTable.vue'
	import QrcodeDialog from './components/QrcodeDialog.vue'

	export default {
		name: 'QrcodeList',
		components: {
			SearchForm,
			QrcodeTable,
			QrcodeDialog
		},
		mixins: [tableMixin],
		data() {
			return {
				searchParams: {
					userName: '',
					userMobile: '',
					isActive: null
				},
				dialogVisible: false,
				editData: null // null表示新增模式，有值表示编辑模式
			}
		},
		methods: {
			// 加载数据
			loadData() {
				const {
					searchParams: { userName, userMobile, isActive },
					pageIndex,
					pageSize
				} = this
				this.loading = true
				qrcodeManager
					.queryQrcodes(userName, userMobile, isActive, pageIndex, pageSize)
					.then(res => {
						const { total, list } = res
						this.total = total
						this.tableData = list
					})
					.catch(err => {
						this.$message.error(err)
					})
					.then(() => {
						this.loading = false
					})
			},

			// 清空搜索
			handleClear() {
				this.searchParams = {
					userName: '',
					userMobile: '',
					isActive: null
				}
				this.loadData()
			},
			// 新增
			handleAdd() {
				this.editData = null // 清空编辑数据，表示新增模式
				this.dialogVisible = true
			},
			// 编辑
			handleEdit(item) {
				this.editData = { ...item } // 设置编辑数据，表示编辑模式
				this.dialogVisible = true
			},
			// 对话框操作成功（新增/编辑）
			handleDialogSuccess() {
				this.loadData()
			},
			// 切换状态
			handleToggleStatus(item) {
				const newStatus = item.isActive ? 0 : 1
				const action = newStatus ? '启用' : '禁用'

				this.$confirm(`确定要${action}该二维码吗？`, '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					qrcodeManager
						.updateQrcode(item.id, { isActive: newStatus })
						.then(() => {
							this.$message.success(`${action}成功`)
							this.loadData()
						})
						.catch(err => {
							this.$message.error(err)
						})
				})
			},
			// 删除
			handleDelete(item) {
				this.$confirm('确定要删除该二维码吗？删除后无法恢复！', '警告', {
					confirmButtonText: '确定删除',
					cancelButtonText: '取消',
					type: 'error'
				}).then(() => {
					qrcodeManager
						.deleteQrcode(item.id)
						.then(() => {
							this.$message.success('删除成功')
							this.loadData()
						})
						.catch(err => {
							this.$message.error(err)
						})
				})
			}
		},
		created() {
			// 页面初始化时不再需要加载所有用户
		}
	}
</script>

<style scoped>
	.card {
		padding: 20px;
	}

	.card-body {
		margin-top: 20px;
	}
</style>
