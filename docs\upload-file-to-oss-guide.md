# uploadFileToOSS 通用文件上传函数使用指南

## 概述

`uploadFileToOSS` 是一个通用的文件上传函数，支持将文件上传到阿里云OSS存储服务。该函数通过可选的配置参数支持不同的业务场景和验证需求。

## 函数签名

```javascript
async function uploadFileToOSS(req, defaultNamespace = 'common', options = {})
```

## 参数说明

### 基础参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `req` | `IncomingMessage` | - | HTTP请求对象，包含文件数据 |
| `defaultNamespace` | `string` | `'common'` | 默认的OSS存储目录 |
| `options` | `Object` | `{}` | 可选的上传配置参数 |

### 配置参数 (options)

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `maxFileSize` | `number` | `100 * 1024 * 1024` | 文件大小限制（字节），默认100MB |
| `allowedMimeTypes` | `string[]` | `null` | 允许的MIME类型数组 |
| `allowedExtensions` | `RegExp` | `null` | 允许的文件扩展名正则表达式 |
| `filePrefix` | `string` | `''` | 文件名前缀 |
| `errorMessage` | `string` | `'文件类型不符合要求'` | 自定义的文件类型错误信息 |

## 返回值

```javascript
{
  url: string,        // OSS文件访问URL
  fields: {           // 表单字段和文件元数据
    originalname: string,  // 原始文件名
    size: number,          // 文件大小
    mimetype: string,      // MIME类型
    ...otherFields         // 其他表单字段
  }
}
```

## 使用示例

### 1. 基础用法（通用文件上传）

```javascript
const commonController = require('../controllers/commonController')

// 最简单的用法，使用默认配置
router.post('/upload', async (req, res) => {
  try {
    const data = await commonController.uploadFileToOSS(req)
    res.sendSuccess(data)
  } catch (error) {
    res.sendMessage(error.message)
  }
})
```

### 2. 指定存储目录

```javascript
// 上传到指定的OSS目录
router.post('/upload', async (req, res) => {
  try {
    const data = await commonController.uploadFileToOSS(req, 'documents')
    res.sendSuccess(data)
  } catch (error) {
    res.sendMessage(error.message)
  }
})
```

### 3. 图片上传（二维码场景）

```javascript
// 二维码图片上传，严格的文件验证
router.post('/qrcode/upload', async (req, res) => {
  try {
    const qrcodeOptions = {
      maxFileSize: 2 * 1024 * 1024, // 2MB限制
      allowedMimeTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
      allowedExtensions: /\.(jpeg|jpg|png|gif)$/i,
      filePrefix: 'qrcode',
      errorMessage: '只允许上传图片文件 (jpeg, jpg, png, gif)'
    }
    
    const data = await commonController.uploadFileToOSS(req, 'qrcode', qrcodeOptions)
    res.sendSuccess({
      url: data.url,
      filename: data.url.split('/').pop(),
      originalname: data.fields.originalname,
      size: data.fields.size
    })
  } catch (error) {
    res.sendMessage(error.message)
  }
})
```

### 4. 文档上传

```javascript
// 文档上传，支持多种文档格式
router.post('/document/upload', async (req, res) => {
  try {
    const documentOptions = {
      maxFileSize: 10 * 1024 * 1024, // 10MB限制
      allowedMimeTypes: [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain'
      ],
      allowedExtensions: /\.(pdf|doc|docx|txt)$/i,
      filePrefix: 'doc',
      errorMessage: '只允许上传文档文件 (pdf, doc, docx, txt)'
    }
    
    const data = await commonController.uploadFileToOSS(req, 'documents', documentOptions)
    res.sendSuccess(data)
  } catch (error) {
    res.sendMessage(error.message)
  }
})
```

### 5. 头像上传

```javascript
// 用户头像上传
router.post('/avatar/upload', async (req, res) => {
  try {
    const avatarOptions = {
      maxFileSize: 1 * 1024 * 1024, // 1MB限制
      allowedMimeTypes: ['image/jpeg', 'image/png'],
      allowedExtensions: /\.(jpeg|jpg|png)$/i,
      filePrefix: 'avatar',
      errorMessage: '头像只支持 JPG 和 PNG 格式'
    }
    
    const data = await commonController.uploadFileToOSS(req, 'avatars', avatarOptions)
    res.sendSuccess(data)
  } catch (error) {
    res.sendMessage(error.message)
  }
})
```

## 配置详解

### 文件大小限制

```javascript
const options = {
  maxFileSize: 5 * 1024 * 1024  // 5MB = 5 * 1024 * 1024 字节
}
```

### MIME类型验证

```javascript
const options = {
  allowedMimeTypes: [
    'image/jpeg',
    'image/png',
    'image/gif',
    'application/pdf'
  ]
}
```

### 文件扩展名验证

```javascript
const options = {
  // 只允许图片文件
  allowedExtensions: /\.(jpeg|jpg|png|gif)$/i,
  
  // 只允许文档文件
  allowedExtensions: /\.(pdf|doc|docx|txt)$/i,
  
  // 允许图片和文档
  allowedExtensions: /\.(jpeg|jpg|png|gif|pdf|doc|docx)$/i
}
```

### 文件名前缀

```javascript
const options = {
  filePrefix: 'product'  // 生成的文件名: product-originalname-timestamp.ext
}
```

## 错误处理

### 常见错误类型

1. **文件大小超限**
   ```
   文件大小不能超过2MB
   ```

2. **文件类型不符**
   ```
   只允许上传图片文件 (jpeg, jpg, png, gif)
   ```

3. **文件解析失败**
   ```
   文件解析失败: [具体错误信息]
   ```

4. **OSS上传失败**
   ```
   文件处理或上传OSS失败: [具体错误信息]
   ```

### 错误处理最佳实践

```javascript
router.post('/upload', async (req, res) => {
  try {
    const data = await commonController.uploadFileToOSS(req, 'namespace', options)
    res.sendSuccess(data)
  } catch (error) {
    console.error('文件上传失败:', error)
    
    // 根据错误类型返回不同的错误信息
    if (error.message.includes('文件大小')) {
      res.sendMessage('文件太大，请选择较小的文件')
    } else if (error.message.includes('文件类型')) {
      res.sendMessage('文件格式不支持，请选择正确的文件类型')
    } else {
      res.sendMessage('文件上传失败，请重试')
    }
  }
})
```

## 存储结构

### OSS存储路径规则

```
bucket/namespace/YYYYMMDD/prefix-filename-timestamp.ext
```

**示例**:
- 二维码: `qwc-prd/qrcode/20241201/qrcode-myfile-1640995200000.jpg`
- 文档: `qwc-prd/documents/20241201/doc-report-1640995200000.pdf`
- 头像: `qwc-prd/avatars/20241201/avatar-user123-1640995200000.png`

### 访问URL格式

```
https://qwc-prd.oss-cn-hangzhou.aliyuncs.com/namespace/YYYYMMDD/filename.ext
```

## 性能优化建议

### 1. 合理设置文件大小限制

```javascript
// 根据业务需求设置合适的大小限制
const options = {
  maxFileSize: 2 * 1024 * 1024  // 图片: 2MB
  maxFileSize: 10 * 1024 * 1024 // 文档: 10MB
  maxFileSize: 50 * 1024 * 1024 // 视频: 50MB
}
```

### 2. 精确的文件类型验证

```javascript
// 同时验证MIME类型和扩展名，提高安全性
const options = {
  allowedMimeTypes: ['image/jpeg', 'image/png'],
  allowedExtensions: /\.(jpeg|jpg|png)$/i
}
```

### 3. 使用有意义的命名空间

```javascript
// 按业务模块划分命名空间
await uploadFileToOSS(req, 'user-avatars')    // 用户头像
await uploadFileToOSS(req, 'product-images')  // 商品图片
await uploadFileToOSS(req, 'qrcode')          // 二维码
await uploadFileToOSS(req, 'documents')       // 文档
```

## 向后兼容性

该函数完全向后兼容，现有的调用方式无需修改：

```javascript
// 原有调用方式仍然有效
const data = await uploadFileToOSS(req)
const data = await uploadFileToOSS(req, 'namespace')
```

新的配置参数是可选的，不会影响现有功能的正常使用。
