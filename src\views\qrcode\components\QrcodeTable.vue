<template>
	<div>
		<el-table
			v-loading="loading"
			:data="tableData"
			border
			fit
			highlight-current-row
			style="width: 100%"
		>
			<el-table-column label="ID" prop="id" align="center" width="60"></el-table-column>
			<el-table-column label="二维码图片" prop="qrCodeUrl" align="center" width="120">
				<template slot-scope="{ row }">
					<el-image
						v-if="row.qrCodeUrl"
						:src="row.qrCodeUrl"
						:preview-src-list="[row.qrCodeUrl]"
						style="width: 80px; height: 80px"
						fit="cover"
					></el-image>
					<span v-else>暂无图片</span>
				</template>
			</el-table-column>
			<el-table-column label="归属用户" prop="userName" align="center" width="120">
				<template slot-scope="{ row }">
					<div v-if="row.userId">
						<div>{{ row.userName }}</div>
						<div style="color: #999; font-size: 12px">{{ row.userMobile }}</div>
					</div>
					<el-tag v-else type="warning">系统级</el-tag>
				</template>
			</el-table-column>
			<el-table-column label="用户类型" prop="userType" align="center" width="100">
				<template slot-scope="{ row }">
					<div v-if="row.userId">
						<div v-if="row.userType === 100">管理员</div>
						<div v-else-if="row.userType === 10">信息收集员</div>
						<div v-else-if="row.userType === 1">收购商</div>
						<div v-else-if="row.userType === 2">粮庄</div>
						<div v-else-if="row.userType === 3">设备商</div>
						<div v-else>普通用户</div>
					</div>
					<span v-else>-</span>
				</template>
			</el-table-column>
			<el-table-column label="描述信息" prop="description" align="center" min-width="200">
				<template slot-scope="{ row }">
					<div style="word-break: break-all">{{ row.description || '-' }}</div>
				</template>
			</el-table-column>
			<el-table-column label="生效状态" prop="isActive" align="center" width="100">
				<template slot-scope="{ row }">
					<el-tag :type="row.isActive ? 'success' : 'danger'">
						{{ row.isActive ? '启用' : '禁用' }}
					</el-tag>
				</template>
			</el-table-column>
			<el-table-column label="创建时间" prop="createTime" align="center" width="160">
				<template slot-scope="{ row }">{{
					new Date(row.createTime).format('yyyy-MM-dd hh:mm:ss')
				}}</template>
			</el-table-column>
			<el-table-column label="操作" align="center" width="180">
				<template slot-scope="{ row }">
					<span class="el_btn_box">
						<el-button
							:type="row.isActive ? 'warning' : 'success'"
							size="small"
							@click="handleToggleStatus(row)"
						>
							{{ row.isActive ? '禁用' : '启用' }}
						</el-button>
						<el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
						<el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
					</span>
				</template>
			</el-table-column>
		</el-table>
		<Pagination
			v-if="total > 0"
			:total="total"
			:pageIndex.sync="currentPageIndex"
			:pageSize.sync="currentPageSize"
			@pagination="handlePagination"
		/>
	</div>
</template>

<script>
	import Pagination from '@/components/Pagination'

	export default {
		name: 'QrcodeTable',
		components: { Pagination },
		props: {
			loading: {
				type: Boolean,
				default: false
			},
			tableData: {
				type: Array,
				default: () => []
			},
			total: {
				type: Number,
				default: 0
			},
			pageIndex: {
				type: Number,
				default: 0
			},
			pageSize: {
				type: Number,
				default: 10
			}
		},
		data() {
			return {
				currentPageIndex: 0,
				currentPageSize: 10
			}
		},
		watch: {
			pageIndex: {
				handler(val) {
					this.currentPageIndex = val
				},
				immediate: true
			},
			pageSize: {
				handler(val) {
					this.currentPageSize = val
				},
				immediate: true
			},
			currentPageIndex(val) {
				this.$emit('update:pageIndex', val)
			},
			currentPageSize(val) {
				this.$emit('update:pageSize', val)
			}
		},
		methods: {
			// 切换状态
			handleToggleStatus(row) {
				this.$emit('toggle-status', row)
			},
			// 编辑
			handleEdit(row) {
				this.$emit('edit', row)
			},
			// 删除
			handleDelete(row) {
				this.$emit('delete', row)
			},
			// 分页
			handlePagination() {
				this.$emit('pagination')
			}
		}
	}
</script>

<style scoped>
	.el_btn_box {
		display: flex;
		gap: 5px;
		flex-wrap: wrap;
		justify-content: center;
	}
</style>
