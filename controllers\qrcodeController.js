const { QRCODE, USER } = require('../db').tableNames
const { resFilter, getKeysObj } = require('../utils/filter')
const dbController = require('./dbController')

// 二维码数据更新字段
const qrcodeUpdateKeys = [
	'userId',
	'userName',
	'userMobile',
	'userType',
	'qrCodeUrl',
	'description',
	'isActive'
]

// 二维码数据创建字段
const qrcodeCreateKeys = [
	'userId',
	'userName',
	'userMobile',
	'userType',
	'qrCodeUrl',
	'description',
	'isActive'
]

/**
 * 查询二维码列表
 * @param {string} userName - 用户名（可选）
 * @param {string} userMobile - 用户手机号（可选）
 * @param {number} isActive - 是否生效（可选，1:生效, 0:不生效）
 * @param {number} pageIndex - 页码
 * @param {number} pageSize - 每页数量
 * @returns {Promise} 返回二维码列表数据
 */
function queryQrcodes(userName, userMobile, isActive, pageIndex, pageSize) {
	return new Promise((resolve, reject) => {
		const options = {}

		// 构建查询条件
		if (userName) {
			options['userName.like'] = userName
		}
		if (userMobile) {
			options['userMobile.like'] = userMobile
		}
		if (isActive !== null && isActive !== undefined) {
			options.isActive = isActive
		}

		// 先获取总数
		dbController.count(QRCODE, options, (err, rows) => {
			if (err) {
				reject(err)
			} else {
				const total = rows[0]['count(*)']

				// 如果总数为0，直接返回空结果
				if (total === 0) {
					resolve({ total: 0, list: [] })
					return
				}

				// 分页查询数据
				const limitOptions = {
					pageIndex,
					pageSize,
					orderBy: 'createTime',
					orderSort: 'DESC'
				}

				dbController.query(QRCODE, options, limitOptions, (err, rows) => {
					if (err) {
						reject(err)
					} else {
						// 格式化返回数据
						const formattedList = rows.map(item => ({
							id: item.id,
							userId: item.userId,
							userName: item.userName,
							userMobile: item.userMobile,
							userType: item.userType,
							qrCodeUrl: item.qrCodeUrl,
							description: item.description,
							isActive: item.isActive,
							createTime: item.createTime,
							updateTime: item.updateTime
						}))
						resolve({ total, list: rows.map(item => resFilter(item)) })
					}
				})
			}
		})
	})
}

/**
 * 创建新的二维码
 * @param {Object} qrcodeData - 二维码数据
 * @param {number} qrcodeData.userId - 用户ID（可选，为空表示系统级二维码）
 * @param {string} qrcodeData.qrCodeUrl - 二维码图片URL
 * @param {string} qrcodeData.description - 描述信息
 * @param {number} qrcodeData.isActive - 是否生效（1:生效, 0:不生效）
 * @returns {Promise} 返回创建结果
 */
function createQrcode(qrcodeData) {
	return new Promise((resolve, reject) => {
		// 如果有用户ID，需要获取用户信息
		if (qrcodeData.userId) {
			getUserInfo(qrcodeData.userId)
				.then(userInfo => {
					const insertData = {
						...getKeysObj(qrcodeData, qrcodeCreateKeys),
						userName: userInfo.name,
						userMobile: userInfo.mobile,
						userType: userInfo.type
					}

					dbController
						.insert(QRCODE, insertData)
						.then(() => {
							resolve('二维码创建成功')
						})
						.catch(err => {
							reject(err)
						})
				})
				.catch(err => {
					reject(err)
				})
		} else {
			// 系统级二维码
			const insertData = {
				...getKeysObj(qrcodeData, qrcodeCreateKeys)
			}

			dbController
				.insert(QRCODE, insertData)
				.then(() => {
					resolve('二维码创建成功')
				})
				.catch(err => {
					reject(err)
				})
		}
	})
}

/**
 * 更新二维码信息
 * @param {number} id - 二维码ID
 * @param {Object} updateData - 更新数据
 * @param {string} updateData.qrCodeUrl - 二维码图片URL（可选）
 * @param {string} updateData.description - 描述信息（可选）
 * @param {number} updateData.isActive - 是否生效（可选）
 * @returns {Promise} 返回更新结果
 */
function updateQrcode(id, updateData) {
	return new Promise((resolve, reject) => {
		const updateObj = {
			...getKeysObj(updateData, qrcodeUpdateKeys)
		}

		dbController
			.update(QRCODE, { id }, updateObj)
			.then(() => {
				resolve('二维码更新成功')
			})
			.catch(err => {
				reject(err)
			})
	})
}

/**
 * 删除二维码
 * @param {number} id - 二维码ID
 * @returns {Promise} 返回删除结果
 */
function deleteQrcode(id) {
	return new Promise((resolve, reject) => {
		dbController
			.delete(QRCODE, { id })
			.then(() => {
				resolve('二维码删除成功')
			})
			.catch(err => {
				reject(err)
			})
	})
}

/**
 * 获取二维码详情
 * @param {number} id - 二维码ID
 * @returns {Promise} 返回二维码详情
 */
function getQrcodeDetail(id) {
	return new Promise((resolve, reject) => {
		dbController
			.queryOne(QRCODE, { id })
			.then(result => {
				if (result) {
					const formattedResult = {
						id: result.id,
						userId: result.userId,
						userName: result.userName,
						userMobile: result.userMobile,
						userType: result.userType,
						qrCodeUrl: result.qrCodeUrl,
						description: result.description,
						isActive: result.isActive,
						createTime: result.createTime,
						updateTime: result.updateTime
					}
					resolve(formattedResult)
				} else {
					reject('二维码不存在')
				}
			})
			.catch(err => {
				reject(err)
			})
	})
}

/**
 * 获取所有用户列表（用于下拉选择）
 * @returns {Promise} 返回用户列表
 */
function getAllUsers() {
	return new Promise((resolve, reject) => {
		const sql = `SELECT id, name, mobile, type FROM ${USER} WHERE 1=1 ORDER BY id DESC`

		dbController
			.dbConnect(sql)
			.then(result => {
				const formattedResult = result.map(item => ({
					id: item.id,
					name: item.name,
					mobile: item.mobile,
					type: item.type
				}))
				resolve(formattedResult)
			})
			.catch(err => {
				reject(err)
			})
	})
}

/**
 * 批量更新二维码状态
 * @param {Array} ids - 二维码ID数组
 * @param {number} isActive - 是否生效（1:生效, 0:不生效）
 * @returns {Promise} 返回批量更新结果
 */
function batchUpdateQrcodeStatus(ids, isActive) {
	return new Promise((resolve, reject) => {
		if (!ids || ids.length === 0) {
			reject('请选择要更新的二维码')
			return
		}

		const sql = `UPDATE ${QRCODE} SET is_active = ?, update_time = CURRENT_TIMESTAMP WHERE id IN (${ids
			.map(() => '?')
			.join(',')})`
		const params = [isActive, ...ids]

		dbController
			.dbConnect(sql, params)
			.then(() => {
				resolve('批量更新成功')
			})
			.catch(err => {
				reject(err)
			})
	})
}

/**
 * 获取用户信息
 * @param {number} userId - 用户ID
 * @returns {Promise} 返回用户信息
 */
function getUserInfo(userId) {
	return new Promise((resolve, reject) => {
		dbController
			.queryOne(USER, { id: userId })
			.then(result => {
				if (result) {
					resolve({
						id: result.id,
						name: result.name,
						mobile: result.mobile,
						type: result.type
					})
				} else {
					reject('用户不存在')
				}
			})
			.catch(err => {
				reject(err)
			})
	})
}

module.exports = {
	queryQrcodes,
	createQrcode,
	updateQrcode,
	deleteQrcode,
	getQrcodeDetail,
	getAllUsers,
	batchUpdateQrcodeStatus
}
