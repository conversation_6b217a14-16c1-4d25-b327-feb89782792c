<template>
    <div class="navbar_root" :class="{ sidebar_collapse: $store.state.collapseMenu }">
        <span class="navbar_item navbar_btn" @click="$store.commit('SIDEBAR_COLLAPSE_CHANGE')">
            <i class="el-icon-s-fold"></i>
        </span>
        <span class="navbar_item navbar_submenu">{{ submenu }}</span>
        <span class="navbar_item navbar_logout fr" @click="logout">退出</span>
        <span class="navbar_item user_name fr">{{$store.state.admin.username}}</span>
        <span class="navbar_item user_photo fr">
            <img :src="$store.state.admin.avatar" alt="nongzhou">
        </span>
    </div>
</template>

<script>

import contextManager from '@/manager/contextManager'

export default {
    computed:{
        submenu(){
            return this.$route.matched.map(item => item.meta.title).join(' / ');
        }
    },
    methods:{
        logout() {
            contextManager.logout();
            this.$store.commit('LOGOUT');
        }
    }
}
</script>

<style lang="scss">
.navbar_root{
    width: 100%;
    height: 56px;
    line-height: 55px;
    padding-left: 180px;
    border-bottom: 1px solid #e1e1e1;
    background: #f4f6f9;
    box-sizing: border-box;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 99;
    .navbar_item{
        display: inline-block;
        vertical-align: middle;
    }
    .navbar_btn{
        font-size: 24px;
        width: 60px;
        text-align: center;
        cursor: pointer;
    }
    .user_name{
        font-size: 18px;
        margin-right: 20px;
    }
    .user_photo{
        width: 44px;
        height: 44px;
        margin: 6px 10px;
        img{
            display: block;
            width: 100%;
        }
        border-radius: 25px;
        overflow: hidden;
    }
    .navbar_submenu{
        display: inline-block;
        margin-left: 10px;
        font-size: 16px;
    }
    .navbar_logout{
        font-size: 16px;
        color: #999;
        padding: 0 20px;
        cursor: pointer;
        &:hover{
            color: #333333;
        }
    }
    &.sidebar_collapse{
        padding-left: 64px;
        .navbar_btn{
            transform: rotate(-180deg);
        }
    }
}
</style>