<template>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<div class="content-header">
			<div class="container-fluid">
				<div class="row mb-2">
					<div class="col-sm-6">
						<h1 class="m-0 text-dark">{{ title }}</h1>
					</div><!-- /.col -->
					<div class="col-sm-6">
						<ol class="breadcrumb float-sm-right">
							<li class="breadcrumb-item"
								v-for="(item, index) in breadcrumbItems"
								:key="index"
								:class="{ active: index === breadcrumbItems.length -1 }"
							>{{ item }}</li>
						</ol>
					</div><!-- /.col -->
				</div><!-- /.row -->
			</div><!-- /.container-fluid -->
		</div>
		<!-- /.content-header -->

		<!-- Main content -->
		<section class="content">
			<div class="container-fluid">
				<router-view />
			</div>
		</section>
		<!-- /.content -->
	</div>
	<!-- /.content-wrapper -->
</template>

<script>
export default {
	computed: {
		title(){
			return this.$store.state.routerNow.meta.title
		},
		breadcrumbItems(){
			let arr = [];
			this.$store.state.routerNow.matched.forEach(item => {
				if(item.meta.title){
					arr.push(item.meta.title)
				}
			});
			return arr;
		}
	}
}
</script>