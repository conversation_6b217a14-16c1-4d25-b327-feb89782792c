import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

import Login from '@/views/Login.vue'
import OrderList from '@/views/order/List.vue'
import CropCompany from '@/views/price/Company.vue'
import CompanyDetail from '@/views/price/CompanyDetail.vue'
import CropPrice from '@/views/price/CropPrice.vue'
import CropOther from '@/views/price/CropOther.vue'
import Transport from '@/views/transport/index.vue'
import UserList from '@/views/user/List'
import InfoList from '@/views/info/List'
import OpenList from '@/views/home/<USER>'
import CropList from '@/views/system/CropList.vue'
import RegionList from '@/views/system/RegionList.vue'
import SystemConfig from '@/views/system/SystemConfig.vue'
import QrcodeList from '@/views/qrcode/List.vue'

const cropSubmenu = {
	title: '系统设置',
	icon: 'el-icon-setting'
}

const priceSubmenu = {
	title: '价格管理',
	iconText: '￥'
}

// const userSubmenu = {
// 	title: '用户管理',
// 	icon: 'el-icon-user-solid'
// }

const routes = [
	{
		path: '/login',
		name: 'Login',
		component: Login,
		meta: {
			title: '登录',
			requiresAuth: false
		}
	},
	{
		path: '/',
		name: 'SysConfig',
		component: SystemConfig,
		meta: {
			title: '小程启动配置',
			submenu: cropSubmenu,
			requiresAuth: true
		}
	},
	{
		path: '/crop/list',
		name: 'CropList',
		component: CropList,
		meta: {
			title: '农作物管理',
			submenu: cropSubmenu,
			requiresAuth: true
		}
	},
	{
		path: '/region/list',
		name: 'RegionList',
		component: RegionList,
		meta: {
			title: '价格页区域管理',
			submenu: cropSubmenu,
			requiresAuth: true
		}
	},
	{
		path: '/open/log',
		name: 'OpenManager',
		component: OpenList,
		meta: {
			title: '应用启动统计',
			icon: 'el-icon-switch-button',
			requiresAuth: true
		}
	},
	{
		path: '/order',
		name: 'OrderManager',
		component: OrderList,
		meta: {
			title: '订单列表',
			icon: 'el-icon-menu',
			requiresAuth: true
		}
	},
	{
		path: '/price/company',
		name: 'CropCompany',
		component: CropCompany,
		meta: {
			title: '收购厂商',
			submenu: priceSubmenu,
			requiresAuth: true
		}
	},
	{
		path: '/price/companyDetail',
		name: 'CropCompanyDetail',
		component: CompanyDetail,
		meta: {
			title: '收购标准',
			submenu: priceSubmenu,
			requiresAuth: true
		}
	},
	{
		path: '/price/list',
		name: 'CropPrice',
		component: CropPrice,
		meta: {
			title: '收购价格',
			submenu: priceSubmenu,
			requiresAuth: true
		}
	},
	{
		path: '/price/port',
		name: 'CropOther',
		component: CropOther,
		meta: {
			title: '港口信息',
			submenu: priceSubmenu,
			requiresAuth: true
		}
	},
	{
		path: '/transport/index',
		name: 'Transport',
		component: Transport,
		meta: {
			title: '运输信息',
			submenu: priceSubmenu,
			requiresAuth: true
		}
	},
	{
		path: '/info/list',
		name: 'InfoList',
		component: InfoList,
		meta: {
			title: '资讯管理',
			icon: 'el-icon-news',
			requiresAuth: true
		}
	},
	{
		path: '/user',
		name: 'QwcUser',
		component: UserList,
		meta: {
			title: '用户管理',
			icon: 'el-icon-user-solid',
			requiresAuth: true
		}
	},
	{
		path: '/qrcode',
		name: 'QrcodeManager',
		component: QrcodeList,
		meta: {
			title: '微信群二维码管理',
			icon: 'el-icon-picture',
			requiresAuth: true
		}
	},
	{
		path: '/:pathMatch(.*)*',
		redirect: '/'
	}
]

const router = createRouter({
	history: createWebHistory('/admin/'),
	routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
	const authStore = useAuthStore()
	
	// 如果路由需要认证且用户未登录，重定向到登录页
	if (to.meta.requiresAuth !== false && !authStore.isAuthenticated) {
		if (to.path !== '/login') {
			next('/login')
			return
		}
	}
	
	// 如果已登录用户访问登录页，重定向到首页
	if (to.path === '/login' && authStore.isAuthenticated) {
		next('/')
		return
	}
	
	next()
})

export default router
