<template>
    <div class="login_page" v-loading="loading">
        <div class="login_wrapper">
            <h1>千万仓管理后台</h1>
            <div class="login_box">
                <p class="form_label">请输入账号及密码</p>
                <div class="form_item">
                    <el-input v-model="mobile" maxlength="11" placeholder="请输入手机号" style="width:300px"></el-input>
                </div>
                <div class="form_item">
                    <el-input v-model="password" type="password" maxlength="16" placeholder="请输入密码" style="width:300px"></el-input>
                </div>
                <div class="form_item">
                    <el-button type="primary" @click="login" style="width:300px">登 录</el-button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>

import contextManager from '@/manager/contextManager'

export default {
    data () {
        return {
            mobile: '',
            password: '',
            loading: false
        }
    },
    methods: {
        login () {
            if (!this.mobile) {
                this.$message.warning('请输入账号')
            } else if (!this.password) {
                this.$message.warning('请输入密码')
            } else {
                this.loading = true;
                contextManager.login(this.mobile, this.password).then(data => {
                    this.$store.commit('LOGIN', data)
                    this.loading = false;
                }).catch(err => {
                    this.$message.error(err)
                    this.loading = false;
                })
            }
        }
    }
}
</script>

<style lang="scss">
.login_page {
    width: 100%;
    height: 100%;
    position: fixed;
    left: 0;
    top: 0;
    background: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    h1 {
        font-size: 32px;
        line-height: 2;
        text-align: center;
        font-weight: 400;
    }
    .login_box {
        background: #ffffff;
        border-radius: 4px;
        font-size: 16px;
        padding: 30px 20px;
        width: 300px;
    }
    .form_label {
        line-height: 2;
    }
    .form_item + .form_item {
        margin-top: 15px;
    }
}
</style>