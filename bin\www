require('dotenv').config();
const app = require('../app');
const debug = require('debug')('express-backend:server');
const http = require('http');
const https = require('https');
const fs = require('fs');
const path = require('path');

const httpPort = normalizePort(process.env.NODE_ENV === 'development' ? 3000 : 80);
const httpsPort = 443;
const httpsOption = {
  key: fs.readFileSync(resolve(process.env.SSL_KEY)),
  cert: fs.readFileSync(resolve(process.env.SSL_CERT))
};

function resolve (dir) {
  return path.join(__dirname, dir);
}

function normalizePort (val) {
  const port = parseInt(val, 10);
  if (isNaN(port)) {
    return val;
  }
  if (port >= 0) {
    return port;
  }
  return false;
}

const httpServer = http.createServer((req, res) => {
  if (process.env.NODE_ENV === 'production') {
    // 在生产环境中，将所有 HTTP 请求重定向到 HTTPS
    const host = req.headers['host'].replace(/^www\./, '');
    res.writeHead(301, { 'Location': `https://${host}${req.url}` });
    res.end();
  } else {
    // 在开发环境中，直接使用 Express 应用
    app(req, res);
  }
});

const httpsServer = https.createServer(httpsOption, app);

httpServer.listen(httpPort, () => {
  const addr = httpServer.address();
  const bind = typeof addr === 'string' ? 'pipe ' + addr : 'port ' + addr.port;
  debug('HTTP Server listening on ' + bind);
});

httpsServer.listen(httpsPort, () => {
  const addr = httpsServer.address();
  const bind = typeof addr === 'string' ? 'pipe ' + addr : 'port ' + addr.port;
  debug('HTTPS Server listening on ' + bind);
});

httpServer.on('error', err => {
  if (err.syscall !== 'listen') {
    throw err;
  }
  const bind = typeof httpPort === 'string' ? 'Pipe ' + httpPort : 'Port ' + httpPort;
  switch (err.code) {
    case 'EACCES':
      console.error(`${bind} requires elevated privileges`);
      process.exit(1);
      break;
    case 'EADDRINUSE':
      console.error(`${bind} is already in use`);
      process.exit(1);
      break;
    default:
      throw err;
  }
});

httpsServer.on('error', err => {
  if (err.syscall !== 'listen') {
    throw err;
  }
  switch (err.code) {
    case 'EACCES':
      console.error(`Port ${httpsPort} requires elevated privileges`);
      process.exit(1);
      break;
    case 'EADDRINUSE':
      console.error(`Port ${httpsPort} is already in use`);
      process.exit(1);
      break;
    default:
      throw err;
  }
});