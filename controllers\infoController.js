//  千万仓咨询板块
const axios = require('axios')
const qs = require('qs')
const cheerio = require('cheerio')
const dbController = require('./dbController')
const cropController = require('./cropController')
const ossController = require('./ossController')
const { resFilter, getKeysObj } = require('../utils/filter')
const { INFORMATION } = require('../db').tableNames
const { INFO_SOURCE } = require('../constant/enum')
const {
	ScraperChinasoybean,
	ScraperChinaCotton,
	ScraperSDLscb,
	ScraperHBLswz,
	ScraperJLLswz,
	ScraperXJLswz,
	ScraperNMGLswz
} = require('../services/scraper')
const scraperChinasoybean = new ScraperChinasoybean()
const scraperChinaCotton = new ScraperChinaCotton()
const scraperSDLscb = new ScraperSDLscb()
const scraperHBLswz = new ScraperHBLswz()
const scraperJLLswz = new ScraperJLLswz()
const scraperXJLswz = new ScraperXJLswz()
const scraperNMGLswz = new ScraperNMGLswz()

const X_FORM_HEADER = {
	'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
}
// 国家粮食信息交易中心的数据是同一个 Api 来提供的
const CENTER_TRADE_API_URL = 'http://www.grainmarket.com.cn/centerweb/getData'
const infoTableKeys = [
	// 新增和编辑时可以操作的表单字段
	'title',
	'cropIds',
	'content',
	'source',
	'sourceId',
	'author',
	'photos',
	'files',
	'originalLink',
	'summary',
	'publishTime',
	'category'
]

function addInfo(newInfo) {
	return new Promise(function (resolve, reject) {
		dbController.countId(INFORMATION, { originalLink: newInfo.originalLink }).then(total => {
			if (total > 0) {
				reject('资讯已存在')
			} else {
				const newData = getKeysObj(newInfo, infoTableKeys)
				dbController
					.add(INFORMATION, newData)
					.then(() => {
						resolve('资讯添加成功')
					})
					.catch(err => reject(err))
			}
		})
	})
}

// 对于初始化和爬取录入过程，不需要重复检查，可直接录入
function addInfoWithoutCheck(newInfo) {
	const newData = getKeysObj(newInfo, infoTableKeys)
	return dbController.add(INFORMATION, newData)
}

function editInfo(id, newInfo) {
	return new Promise(function (resolve, reject) {
		dbController.countId(INFORMATION, { id }).then(total => {
			if (total === 0) {
				reject('数据不存在')
			} else {
				const newData = getKeysObj(newInfo, infoTableKeys)
				dbController
					.update(INFORMATION, { id }, newData)
					.then(() => {
						resolve('资讯更新成功')
					})
					.catch(err => reject(err))
			}
		})
	})
}

function deleteInfo(id) {
	return dbController.deleteById(INFORMATION, id)
}

// 查询单个资讯，可以返回全部的字段
function getInfoById(id) {
	return dbController.queryById(INFORMATION, id).then(data => resFilter(data))
}

// 列表查询，将不返回 content 字段
function getInfoList(pageIndex, pageSize, reqBody) {
	const { source, publishTime, cropId, title, summary } = reqBody
	const queryOptions = {
		source: source && source.split(','),
		publishTime,
		'cropIds.findIn': cropId,
		'title.like': title,
		'summary.like': summary
	}
	return new Promise(function (resolve, reject) {
		dbController.countId(INFORMATION, queryOptions).then(total => {
			if (total === 0) {
				resolve({
					total: 0,
					list: []
				})
			} else {
				// 列表查询，指定特定字段来进行查询
				const columns = ['id', 'updateTime', 'createTime']
					.concat(infoTableKeys)
					.filter(name => name != 'content')
				dbController
					.queryColumns(INFORMATION, columns, queryOptions, {
						pageIndex,
						pageSize,
						orderBy: 'publish_time'
					})
					.then(rows => {
						resolve({
							total,
							list: rows.map(item => resFilter(item))
						})
					})
					.catch(err => {
						reject(err)
					})
			}
		})
	})
}

// 在爬取数据的过程中，一旦发现是网络资源的图片或视频等，需要上传至阿里云OSS后，使用新的阿里云链接
function getNewSourceUrl(url) {
	return ossController.uploadToOSS(url, 'info')
}

// 根据标题或文章中提取的文字组尝试匹配关联的作物
function matchCropByText(cropArr, textArr) {
	const idArr = []
	for (let index = 0; index < cropArr.length; index++) {
		const { id, name } = cropArr[index]
		textArr.forEach(text => {
			if (text.includes(name) && !idArr.includes(id)) {
				idArr.push(id)
			}
		})
	}
	return idArr.join(',')
}
// 首次爬取供需分析的数据，并录入数据库中
// 初始化过程拉去前面的 3 页的数据
function initSupplyDemandSituationData() {
	return new Promise(async function (resolve, reject) {
		const hasDataCount = await dbController.countId(INFORMATION, {
			source: INFO_SOURCE.ChineseAgriculturalSupplyAndDemandEstimates
		})
		if (hasDataCount > 0) {
			return reject('交易中心的数据已初始化，不可重复录入')
		}
		querySupplyDemandSituationListData(false).then(async ({ list, cropArr }) => {
			for (let index = list.length - 1; index >= 0; index--) {
				const infoData = list[index]
				const { author, content, strongTextArr, files } =
					await buildSupplyDemandSituationSinglePageData(infoData.originalLink)
				// 通过标题获取不到具体作物，尝试通过内容获取
				if (!infoData.cropIds) {
					infoData.cropIds = matchCropByText(cropArr, strongTextArr)
				}
				try {
					await addInfoWithoutCheck({
						...infoData,
						author,
						content,
						files
					})
					console.log(`第${index}条数据录入成功`)
				} catch (error) {
					reject(error)
					return
				}
			}
			resolve(`数据录入成功，共录入了${list.length}条数据`)
		})
	})
}

// 首次爬取交易中心的数据，并录入到数据库中
// 初始化的过程中只拉去前面 40 条的数据
function initTradeCenterData() {
	return new Promise(async function (resolve, reject) {
		const hasDataCount = await dbController.countId(INFORMATION, {
			source: INFO_SOURCE.NationalGrainTradeCenter
		})
		if (hasDataCount > 0) {
			return reject('粮食中心的数据已初始化，不可重复录入')
		}
		queryTradeCenterData(40)
			.then(async list => {
				// 优先向数据库中插入老的数据
				for (let index = list.length - 1; index >= 0; index--) {
					const infoData = list[index]
					// 网络资源都需要转换成阿里云的oss资源链接
					const photos = await ossController.uploadToOSS(infoData.photos, 'info')
					await addInfoWithoutCheck({
						...infoData,
						photos
					})
					console.log(`第${index}条数据录入成功`)
				}
				resolve(`数据录入成功，共录入了${list.length}条数据`)
			})
			.catch(err => reject(err))
	})
}

// 尝试爬取最新的交易资讯，并录入数据库
function tryInsertLatestSupplyDemandData() {
	return new Promise(function (resolve, reject) {
		querySupplyDemandSituationListData(true)
			.then(async ({ list, cropArr }) => {
				let addCount = 0
				let errorCount = 0
				for (const info of list) {
					try {
						// 先确认同一个资源下是否存在相同id的资讯
						const total = await dbController.countId(INFORMATION, {
							source: INFO_SOURCE.ChineseAgriculturalSupplyAndDemandEstimates,
							sourceId: info.sourceId
						})
						if (total === 0) {
							const { author, content, strongTextArr, files } =
								await buildSupplyDemandSituationSinglePageData(info.originalLink)
							// 通过标题获取不到具体作物，尝试通过内容获取
							if (!info.cropIds) {
								info.cropIds = matchCropByText(cropArr, strongTextArr)
							}
							await addInfoWithoutCheck({
								...info,
								author,
								content,
								files
							})
							addCount++
							console.log(`${info.title}: 成功录入`)
						} else {
							console.log(`${info.title}: 已存在，忽略`)
						}
					} catch (err) {
						console.log(err)
						errorCount++
					}
				}
				resolve({
					total: list.length,
					success: addCount,
					fail: errorCount,
					ignore: list.length - addCount - errorCount
				})
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 尝试插入最新的交易中心的数据，只爬取最前面的10，大约涵盖了过去的半年
function tryInsertLatestTradeCenterData() {
	return new Promise(function (resolve, reject) {
		queryTradeCenterData(10)
			.then(async list => {
				let addCount = 0
				let errorCount = 0
				for (const info of list) {
					try {
						// 先确认同一个资源下是否存在相同id的资讯
						const total = await dbController.countId(INFORMATION, {
							source: INFO_SOURCE.NationalGrainTradeCenter,
							sourceId: info.sourceId
						})
						if (total === 0) {
							// 网络资源都需要转换成阿里云的oss资源链接
							const photos = await ossController.uploadToOSS(info.photos, 'info')
							await addInfoWithoutCheck({
								...info,
								photos
							})
							addCount++
							console.log(`${info.title}: 成功录入`)
						} else {
							console.log(`${info.title}: 已存在，忽略`)
						}
					} catch (error) {
						errorCount++
						reject(error)
					}
				}
				resolve({
					total: list.length,
					success: addCount,
					fail: errorCount,
					ignore: list.length - addCount - errorCount
				})
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 查询中国农业农村信息网的供需形势分析
// http://www.agri.cn/sj/gxxs/index.htm
// 该地址非 AJAX 请求返回数据，需要从页面中去爬取列表数据
// 考虑到比较早的数据已经数据比较老的日期，考虑至爬取前面几页的数据
async function querySupplyDemandSituationListData(firstPageOnly) {
	const cropArr = await cropController.queryCrops(null, null, 1)
	const pathList = ['index.htm']
	// 对于第一次爬取数据，需照顾后面的两页的数据
	// 后续调用更新数据，无需关注后面的页面，只需要确保首页的数据被拉取进行比对更新
	if (!firstPageOnly) {
		pathList.push('index_1.htm', 'index_2.htm')
	}
	const baseUrl = 'http://www.agri.cn/sj'
	const urlTypes = ['gxxs', 'jcyj']
	const allRequests = urlTypes
		.map(type => {
			return pathList.map(htmlName => {
				return axios.get(`${baseUrl}/${type}/${htmlName}`)
			})
		})
		.flat()

	return Promise.all(allRequests).then(resArr => {
		const list = []
		resArr.forEach((res, index) => {
			const html = res.data
			const $ = cheerio.load(html, { xml: { decodeEntities: false } }, false)
			const type = urlTypes[Math.floor(index / pathList.length)] // 提取 type 字段
			$('.list_li_con').each((index, element) => {
				const title = $(element).find('.con_tit').text().trim()
				if (type === 'jcyj' && !title.includes('供需形势分析月报')) {
					return
				}
				const link = $(element).find('.con_tit a').attr('href')
				const sourceId = parseInt(link.split('/').pop().split('_')[1])
				const originalLink = `${baseUrl}/${type}${link.substring(1)}`
				const summary = $(element).find('.con_text').text().trim()
				const publishTime = $(element).find('.con_date_span').text().trim()
				const cropIds = matchCropByText(cropArr, [title])
				list.push({
					title,
					originalLink,
					summary,
					source: INFO_SOURCE.ChineseAgriculturalSupplyAndDemandEstimates,
					sourceId,
					publishTime,
					cropIds
				})
			})
		})
		return { list, cropArr }
	})
}

// 构建一个 供需形势 的网站的一个页面数据
// 其中 strongTextArr / author / content 字段需通过详情页来提取， 其余字段通过列表页来获取
// strongTextArr 提取的文案列表用于判定作物的品类
// 采用 cheerio 工具类使用JQuery的语法进行提取
// http://www.agri.cn/sj/gxxs/202311/t20231130_8130951.htm
function buildSupplyDemandSituationSinglePageData(url) {
	return axios.get(url).then(async res => {
		const html = res.data
		const $ = cheerio.load(html, { xml: { decodeEntities: false } }, false)
		const author = $('.updateInfo_mess .mess_text')
			.last()
			.text()
			.replace(/来源：/, '')
			.trim()
		const content = $('.TRS_Editor').html()
		let htmlFiles = {}
		const links = $('.ArticleDetails').find('a')
		for (let i = 0; i < links.length; i++) {
			const element = links[i]
			const href = $(element).attr('href')
			const text = $(element).text()

			if (href && href.toLowerCase().endsWith('.pdf')) {
				const absoluteSrc = /^(https?:|data:)/i.test(href) ? href : new URL(href, url).href
				try {
					htmlFiles[text] = await ossController.uploadToOSS(absoluteSrc, 'info')
				} catch (error) {
					console.error(`Failed to upload PDF file ${absoluteSrc}:`, error)
				}
			}
		}

		const strongTextArr = []
		// 该详细页中加粗的字体中包含了品类的具体信息，尝试用加粗字体中匹配品类
		$('.TRS_Editor b').each((index, element) => {
			const strongText = $(element).text().trim()
			// 加粗的字体包含品类的信息，将所有加粗的字体提取
			if (strongText) {
				strongTextArr.push(strongText)
			}
		})
		return { author, content, strongTextArr, files: JSON.stringify(htmlFiles) }
	})
}

// http://www.grainmarket.com.cn/centerweb/info/list/5
// 国家粮食交易中心数据拉取
// 该页面通过 ajax 执行数据的拉取，可通过请求直接爬取全量数据
// 接口地址：http://www.grainmarket.com.cn/centerweb/getData
// 接口参数：param: {"m":"tradeCenterOtherNewsList","indexid":"1","pagesize":"150","articleTypeID":"10"}
// 查询最新的 number 条数据
async function queryTradeCenterData(number) {
	const cropArr = await cropController.queryCrops(null, null, 1)
	const data = {
		param: `{"m":"tradeCenterOtherNewsList","indexid":"1","pagesize":"${number}","articleTypeID":"10"}`
	}
	return new Promise(function (resolve, reject) {
		axios
			.post(CENTER_TRADE_API_URL, qs.stringify(data), {
				headers: X_FORM_HEADER
			})
			.then(res => {
				// {
				//     "imgUrl": "https://www.grainmarket.com.cn:8443/Web/Uploads/ArtImg/20221017154718019.PNG",
				//     "no": "7",
				//     "contentUrl": "http://www.grainmarket.com.cn/centerweb/info/detail/1003133",
				//     "publishtime": "2022-10-17",
				//     "articleID": "1003133",
				//     "title": "2022年10月12日国家临时存储小麦交易结果",
				//     "pageViewNum": "1101",
				//     "isSeminar": null
				// }
				// 列表接口中存在图片字段为空的情况，如果为空，需要调用详情页相同接口获取对应的图片
				// https://www.grainmarket.com.cn/centerweb/getData
				const listData = res.data.data
				const needReLoadImgList = listData.filter(item => !item.imgUrl)
				if (needReLoadImgList.length > 0) {
					Promise.all(
						needReLoadImgList.map(item => {
							const data = {
								param: `{"m":"tradeCenterNewsDetail","articleId":"${item.articleID}"}`
							}
							return axios.post(CENTER_TRADE_API_URL, qs.stringify(data), {
								headers: X_FORM_HEADER
							})
						})
					)
						.then(resArr => {
							resArr.forEach((res, index) => {
								const html = res.data.data.Content
								const $ = cheerio.load(html, { xml: { decodeEntities: false } }, false)
								const imgUrl = $('img').attr('src')
								needReLoadImgList[index].imgUrl = imgUrl
							})
							// 数据补齐后，再次返回数据
							doResolve()
						})
						.catch(err => {
							reject(err)
						})
				} else {
					doResolve()
				}
				function doResolve() {
					resolve(
						listData.map(item => ({
							photos: item.imgUrl,
							sourceId: item.articleID,
							source: INFO_SOURCE.NationalGrainTradeCenter,
							title: item.title,
							author: '国家粮食交易中心',
							cropIds: matchCropByText(cropArr, [item.title]),
							publishTime: item.publishtime,
							originalLink: item.contentUrl
						}))
					)
				}
			})
			.catch(err => {
				reject(err)
			})
	})
}

/**
 * 爬取中国大豆产业协会数据
 *
 * @param {boolean} shouldInitializeData  - 是否为初始化数据
 * @param {number} pagesToScrape  - 爬取的页数大小
 * @returns {Promise<object>} - 返回爬取数据的统计信息
 */
async function scrapeChinaSoybeanData(shouldInitializeData = false, pagesToScrape = 1) {
	try {
		if (shouldInitializeData) {
			const existingDataCount = await dbController.countId(INFORMATION, {
				source: INFO_SOURCE.ChinaSoybean
			})
			if (existingDataCount > 0) {
				return '中国大豆产业协会数据已初始化，不可重复录入'
			}
		}
		const crops = await cropController.queryCrops(null, null, 1)

		let [addedCount, errorCount, scrapedData] = [0, 0, []]
		const categoryUrls = [
			{ url: 'news.php?cid=24', category: '国内资讯' },
			{ url: 'cyxx.php?cid=26', category: '国内资讯' },
			{ url: 'cyxx.php?cid=27', category: '国际资讯' }
		]
		for (const { url, category } of categoryUrls) {
			for (let page = 0; page < pagesToScrape; page++) {
				const queryUrl = `${url}&page=${page + 1}`
				try {
					const result = await scraperChinasoybean.scrapeListPage(queryUrl)
					result.forEach(item => (item.category = category))
					scrapedData.push(...result)
				} catch (error) {
					console.error(`抓取列表页 ${queryUrl} 失败:`, error)
				}
			}
		}
		for (const [index, baseInfo] of scrapedData.entries()) {
			try {
				const { title, sourceId, originalLink } = baseInfo
				const existingRecordCount = await dbController.countId(INFORMATION, {
					source: INFO_SOURCE.ChinaSoybean,
					sourceId
				})
				if (existingRecordCount === 0) {
					const { author, content } = await scraperChinasoybean.scrapeDetailPage(originalLink)
					if (!baseInfo.cropIds) {
						baseInfo.cropIds = matchCropByText(crops, [title])
					}
					const detailedInfo = {
						...baseInfo,
						source: INFO_SOURCE.ChinaSoybean,
						author,
						content
					}
					await addInfoWithoutCheck(detailedInfo)
					addedCount++
				} else {
					console.log(`${title}: 已存在，忽略`)
				}
			} catch (error) {
				console.error(`第${index}条数据录入失败:`, error)
				errorCount++
			}
		}
		return {
			total: scrapedData.length,
			success: addedCount,
			fail: errorCount,
			ignore: scrapedData.length - addedCount - errorCount
		}
	} catch (error) {
		throw new Error(`爬取中国大豆产业协会数据失败: ${error.message || error}`)
	}
}

/**
 * 爬取中国棉花协会数据
 *
 * @param {boolean} shouldInitializeData  - 是否为初始化数据
 * @param {number} pagesToScrape  - 爬取的页数大小
 * @returns {Promise<object>} - 返回爬取数据的统计信息
 */
async function scrapeChinaCottonData(shouldInitializeData = false, pagesToScrape = 1) {
	try {
		if (shouldInitializeData) {
			const existingDataCount = await dbController.countId(INFORMATION, {
				source: INFO_SOURCE.ChinaCotton
			})
			if (existingDataCount > 0) {
				return '中国棉花协会数据已初始化，不可重复录入'
			}
		}
		const crops = await cropController.queryCrops(null, null, 1)

		let [addedCount, errorCount, scrapedData] = [0, 0, []]
		const categoryUrls = [
			{ url: 'advise/china_cotton_list', category: '国内棉花' },
			{ url: 'warning/newContent', category: '国内棉花' },
			{ url: 'data/priceContent', category: '国内棉花' },
			{ url: 'advise/textile_cotton_list', category: '纺织信息' },
			{ url: 'data/cottonDataList', category: '纺织信息' },
			{ url: 'advise/inter_cotton_list', category: '国际棉花' },
			{ url: 'data/internationalData', category: '国际棉花' }
		]
		for (const { url, category } of categoryUrls) {
			for (let page = 0; page < pagesToScrape; page++) {
				const queryUrl = `${url}/20/${page + 1}`
				try {
					const result = await scraperChinaCotton.scrapeListPage(queryUrl)
					result.forEach(item => (item.category = category))
					scrapedData.push(...result)
				} catch (error) {
					console.error(`抓取列表页 ${queryUrl} 失败:`, error)
				}
			}
		}
		for (const [index, baseInfo] of scrapedData.entries()) {
			try {
				const { title, sourceId, originalLink } = baseInfo
				const existingRecordCount = await dbController.countId(INFORMATION, {
					source: INFO_SOURCE.ChinaCotton,
					sourceId
				})
				if (existingRecordCount === 0) {
					const { title, author, content } = await scraperChinaCotton.scrapeDetailPage(originalLink)
					if (!baseInfo.cropIds) {
						baseInfo.cropIds = matchCropByText(crops, [title])
					}
					const detailedInfo = {
						...baseInfo,
						source: INFO_SOURCE.ChinaCotton,
						title,
						author,
						content
					}

					await addInfoWithoutCheck(detailedInfo)
					addedCount++
				} else {
					console.log(`${title}: 已存在，忽略`)
				}
			} catch (error) {
				console.error(`第${index}条数据录入失败:`, error)
				errorCount++
			}
		}
		return {
			total: scrapedData.length,
			success: addedCount,
			fail: errorCount,
			ignore: scrapedData.length - addedCount - errorCount
		}
	} catch (error) {
		throw new Error(`爬取中国棉花协会数据失败: ${error.message || error}`)
	}
}

/**
 * 爬取省份粮食和物资储备局数据
 *
 * @param {number} source  - 省份来源
 * @param {boolean} shouldInitializeData  - 是否为初始化数据
 * @param {number} pagesToScrape  - 爬取的页数大小
 * @returns {Promise<object>} - 返回爬取数据的统计信息
 */
async function scraperProvinceGrainData(source, shouldInitializeData = false, pagesToScrape = 1) {
	const provinces = {
		5: {
			name: '山东省',
			source: INFO_SOURCE.SDLscb,
			category: '山东粮食收购信息',
			scraper: scraperSDLscb,
			queryUrl: page => `col/col15411/index.html?pageNum=${page + 1}`
		},
		6: {
			name: '河北省',
			source: INFO_SOURCE.HBLswz,
			category: '粮油市场月度分析报告',
			scraper: scraperHBLswz,
			queryUrl: page => `index${page > 0 ? `_${page + 1}` : ''}.html`
		},
		7: {
			name: '吉林省',
			source: INFO_SOURCE.JLLswz,
			category: '吉林粮食收购信息',
			scraper: scraperJLLswz,
			queryUrl: page => `index${page > 0 ? `_${page + 1}` : ''}.html`
		},
		8: {
			name: '新疆维吾尔自治区',
			source: INFO_SOURCE.XJLswz,
			category: '新疆粮食收购信息',
			scraper: scraperXJLswz,
			queryUrl: page => `/xjgrain/lssgjd/list_left${page > 1 ? `_${page + 1}` : ''}.shtml`
		},
		9: {
			name: '内蒙古自治区',
			source: INFO_SOURCE.NMGLswz,
			category: '稻谷大米信息',
			scraper: scraperNMGLswz,
			queryUrl: page => `index${page > 0 ? `_${page + 1}` : ''}.html`
		}
	}
	const province = provinces[source]
	try {
		if (shouldInitializeData) {
			const existingDataCount = await dbController.countId(INFORMATION, { source })
			if (existingDataCount > 0) {
				return `${province.name}粮食和物资储备局数据已初始化，不可重复录入`
			}
		}
		const crops = await cropController.queryCrops(null, null, 1)

		let [addedCount, errorCount, scrapedData] = [0, 0, []]
		for (let page = 0; page < pagesToScrape; page++) {
			const queryUrl = province.queryUrl(page)
			try {
				const result = await province.scraper.scrapeListPage(queryUrl)
				result.forEach(item => (item.category = province.category))
				scrapedData.push(...result)
			} catch (error) {
				console.error(`抓取列表页 ${queryUrl} 失败:`, error)
			}
		}
		for (const [index, baseInfo] of scrapedData.entries()) {
			try {
				const { title, sourceId, originalLink } = baseInfo
				const existingRecordCount = await dbController.countId(INFORMATION, {
					source,
					sourceId
				})
				if (existingRecordCount === 0) {
					const { title, author, content } = await province.scraper.scrapeDetailPage(originalLink)
					if (!baseInfo.cropIds) {
						baseInfo.cropIds = matchCropByText(crops, [title, content])
					}
					const detailedInfo = {
						...baseInfo,
						source,
						title,
						author,
						content
					}

					await addInfoWithoutCheck(detailedInfo)
					addedCount++
				} else {
					console.log(`${title}: 已存在，忽略`)
				}
			} catch (error) {
				console.error(`第${index}条数据录入失败:`, error)
				errorCount++
			}
		}

		return {
			total: scrapedData.length,
			success: addedCount,
			fail: errorCount,
			ignore: scrapedData.length - addedCount - errorCount
		}
	} catch (error) {
		throw new Error(`爬取${province.name}粮食和物资储备局数据失败: ${error.message || error}`)
	}
}

module.exports = {
	addInfo,
	editInfo,
	deleteInfo,
	getInfoList,
	getInfoById,
	initSupplyDemandSituationData,
	initTradeCenterData,
	tryInsertLatestTradeCenterData,
	tryInsertLatestSupplyDemandData,
	scrapeChinaSoybeanData,
	scrapeChinaCottonData,
	scraperProvinceGrainData
}
