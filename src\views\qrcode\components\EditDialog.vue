<template>
	<el-dialog title="编辑微信群二维码" :visible.sync="visible" width="600px" @close="handleClose">
		<el-form :model="form" :rules="rules" ref="form" label-width="120px">
			<el-form-item label="归属用户">
				<el-input 
					:value="form.userId ? `${form.userName} (${form.userMobile})` : '系统级二维码'"
					readonly
					style="width: 100%"
				></el-input>
				<div style="color: #999; font-size: 12px; margin-top: 5px">
					用户信息不可修改
				</div>
			</el-form-item>
			<el-form-item label="二维码图片" prop="qrCodeUrl">
				<el-upload
					class="qrcode-uploader"
					:action="uploadAction"
					:headers="uploadHeaders"
					:show-file-list="false"
					:on-success="handleUploadSuccess"
					:on-error="handleUploadError"
					:before-upload="beforeUpload"
					accept="image/*"
				>
					<img v-if="form.qrCodeUrl" :src="form.qrCodeUrl" class="qrcode-image" />
					<i v-else class="el-icon-plus qrcode-uploader-icon"></i>
				</el-upload>
				<div style="color: #999; font-size: 12px; margin-top: 5px">
					支持 jpg、png 格式，建议尺寸 400x400 像素
				</div>
			</el-form-item>
			<el-form-item label="生效状态" prop="isActive">
				<el-radio-group v-model="form.isActive">
					<el-radio :label="1">生效</el-radio>
					<el-radio :label="0">不生效</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="描述信息" prop="description">
				<el-input
					v-model="form.description"
					type="textarea"
					:rows="3"
					placeholder="请输入二维码描述信息"
					maxlength="500"
					show-word-limit
				></el-input>
			</el-form-item>
		</el-form>
		<div slot="footer" class="dialog-footer">
			<el-button @click="handleCancel">取 消</el-button>
			<el-button type="primary" @click="handleConfirm" :loading="loading">确 定</el-button>
		</div>
	</el-dialog>
</template>

<script>
import qrcodeManager from '@/manager/qrcodeManager'
import userManager from '@/manager/userManager'

export default {
	name: 'EditDialog',
	props: {
		visible: {
			type: Boolean,
			default: false
		},
		editData: {
			type: Object,
			default: () => ({})
		}
	},
	data() {
		return {
			loading: false,
			form: {
				id: null,
				userId: null,
				userName: '',
				userMobile: '',
				qrCodeUrl: '',
				description: '',
				isActive: 1
			},
			rules: {
				qrCodeUrl: [
					{ required: true, message: '请上传二维码图片', trigger: 'change' }
				],
				isActive: [
					{ required: true, message: '请选择生效状态', trigger: 'change' }
				]
			},
			uploadAction: '/api/v1/admin/qrcode/upload',
			uploadHeaders: {}
		}
	},
	watch: {
		visible(val) {
			if (val) {
				this.initForm()
				this.setUploadHeaders()
			}
		},
		editData: {
			handler(val) {
				if (val && Object.keys(val).length > 0) {
					this.initForm()
				}
			},
			deep: true
		}
	},
	methods: {
		// 初始化表单
		initForm() {
			if (this.editData && Object.keys(this.editData).length > 0) {
				this.form = {
					id: this.editData.id,
					userId: this.editData.userId,
					userName: this.editData.userName || '',
					userMobile: this.editData.userMobile || '',
					qrCodeUrl: this.editData.qrCodeUrl,
					description: this.editData.description || '',
					isActive: this.editData.isActive
				}
			}
		},
		// 设置上传headers
		setUploadHeaders() {
			const token = userManager.getToken()
			if (token) {
				this.uploadHeaders = {
					Authorization: 'Bearer ' + token
				}
			}
		},
		// 上传前验证
		beforeUpload(file) {
			const isImage = file.type.indexOf('image/') === 0
			const isLt2M = file.size / 1024 / 1024 < 2

			if (!isImage) {
				this.$message.error('只能上传图片文件!')
				return false
			}
			if (!isLt2M) {
				this.$message.error('上传图片大小不能超过 2MB!')
				return false
			}
			return true
		},
		// 上传成功回调
		handleUploadSuccess(response) {
			if (response.code === 200) {
				this.form.qrCodeUrl = response.data.url
				this.$message.success('图片上传成功')
			} else {
				this.$message.error(response.message || '上传失败')
			}
		},
		// 上传失败回调
		handleUploadError() {
			this.$message.error('图片上传失败，请重试')
		},
		// 取消
		handleCancel() {
			this.$emit('update:visible', false)
		},
		// 关闭
		handleClose() {
			this.$emit('update:visible', false)
		},
		// 确认编辑
		handleConfirm() {
			this.$refs.form.validate(valid => {
				if (valid) {
					this.loading = true
					const updateData = {
						qrCodeUrl: this.form.qrCodeUrl,
						description: this.form.description,
						isActive: this.form.isActive
					}
					qrcodeManager
						.updateQrcode(this.form.id, updateData)
						.then(() => {
							this.$message.success('更新成功')
							this.$emit('success')
							this.$emit('update:visible', false)
						})
						.catch(err => {
							this.$message.error(err)
						})
						.then(() => {
							this.loading = false
						})
				}
			})
		}
	}
}
</script>

<style scoped>
.qrcode-uploader {
	border: 1px dashed #d9d9d9;
	border-radius: 6px;
	cursor: pointer;
	position: relative;
	overflow: hidden;
	width: 150px;
	height: 150px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.qrcode-uploader:hover {
	border-color: #409eff;
}

.qrcode-uploader-icon {
	font-size: 28px;
	color: #8c939d;
}

.qrcode-image {
	width: 150px;
	height: 150px;
	object-fit: cover;
}
</style>
