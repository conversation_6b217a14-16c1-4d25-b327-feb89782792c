# Technical Stack

## Framework & Core Libraries
- Vue.js 2.6.11
- Vuex 3.4.0 (State management)
- Vue Router 3.2.0 (Routing)
- Element UI 2.14.1 (UI component library)
- Axios 0.21.0 (HTTP client)

## Build System
- Vue CLI 4.5.0
- Webpack (managed by Vue CLI)
- Babel for JavaScript transpilation
- ESLint for code linting
- SCSS with node-sass for styling

## Development Environment
- Node.js (version specified in .nvmrc)
- npm as package manager

## Common Commands

### Installation
```bash
npm install
```

### Development
```bash
npm run dev
```
This starts a development server with hot-reload at http://localhost:8080 with proxy to http://localhost:3000

### Building for Production
```bash
npm run build
```
Builds the app for production to the `dist` folder with minification

### Linting
```bash
npm run lint
```
Lints and fixes files according to ESLint configuration

## Configuration Files
- `vue.config.js` - Vue CLI configuration
- `.eslintrc.js` - ESLint rules
- `babel.config.js` - Babel configuration
- `.browserslistrc` - Browser compatibility targets

## Deployment
The application is deployed using the `deploy.sh` script and is configured to be served from the `/admin` path in production.