const express = require('express')
const router = express.Router()
const { INFO_SOURCE } = require('../../constant/enum')

//控制模块
const infoController = require('../../controllers/infoController')

router.post('/list', function (req, res) {
	// 获取列表
	let pageIndex = req.body.pageIndex || 0
	let pageSize = req.body.pageSize || 0
	infoController
		.getInfoList(pageIndex, pageSize, req.body)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.post('/add', function (req, res) {
	infoController
		.addInfo(req.body)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.put('/update/:id', function (req, res) {
	const { id } = req.params
	infoController
		.editInfo(id, req.body)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.delete('/delete/:id', function (req, res) {
	const { id } = req.params
	infoController
		.deleteInfo(id)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.get('/detail/:id', function (req, res) {
	const { id } = req.params
	infoController
		.getInfoById(id)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

// 主动去刷新一个资源的数据，看看有没有新的数据
router.get('/crawling/:source', function (req, res) {
	const { source } = req.params
	const taskMap = {
		[INFO_SOURCE.ChineseAgriculturalSupplyAndDemandEstimates]: () =>
			infoController.tryInsertLatestSupplyDemandData(),
		[INFO_SOURCE.NationalGrainTradeCenter]: () => infoController.tryInsertLatestTradeCenterData(),
		[INFO_SOURCE.ChinaSoybean]: () => infoController.scrapeChinaSoybeanData(false, 1),
		[INFO_SOURCE.ChinaCotton]: () => infoController.scrapeChinaCottonData(false, 1),
		[INFO_SOURCE.SDLscb]: () =>
			infoController.scraperProvinceGrainData(INFO_SOURCE.SDLscb, false, 1),
		[INFO_SOURCE.HBLswz]: () =>
			infoController.scraperProvinceGrainData(INFO_SOURCE.HBLswz, false, 1),
		[INFO_SOURCE.JLLswz]: () =>
			infoController.scraperProvinceGrainData(INFO_SOURCE.JLLswz, false, 1),
		[INFO_SOURCE.XJLswz]: () =>
			infoController.scraperProvinceGrainData(INFO_SOURCE.XJLswz, false, 1),
		[INFO_SOURCE.NMGLswz]: () =>
			infoController.scraperProvinceGrainData(INFO_SOURCE.NMGLswz, false, 1)
	}
	const task = taskMap[source]

	if (!task) {
		return res.sendMessage('参数有误')
	}

	task()
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

// 初始化一个资源的数据合集
router.get('/init/:source', function (req, res) {
	// 初始化数据，对某一个数据合集进行第一次的爬取
	const { source } = req.params
	const taskMap = {
		[INFO_SOURCE.ChineseAgriculturalSupplyAndDemandEstimates]: () =>
			infoController.initSupplyDemandSituationData(),
		[INFO_SOURCE.NationalGrainTradeCenter]: () => infoController.initTradeCenterData(),
		[INFO_SOURCE.ChinaSoybean]: () => infoController.scrapeChinaSoybeanData(true, 3),
		[INFO_SOURCE.ChinaCotton]: () => infoController.scrapeChinaCottonData(true, 3),
		[INFO_SOURCE.SDLscb]: () =>
			infoController.scraperProvinceGrainData(INFO_SOURCE.SDLscb, true, 3),
		[INFO_SOURCE.HBLswz]: () =>
			infoController.scraperProvinceGrainData(INFO_SOURCE.HBLswz, true, 3),
		[INFO_SOURCE.JLLswz]: () =>
			infoController.scraperProvinceGrainData(INFO_SOURCE.JLLswz, true, 3),
		[INFO_SOURCE.XJLswz]: () =>
			infoController.scraperProvinceGrainData(INFO_SOURCE.XJLswz, true, 3),
		[INFO_SOURCE.NMGLswz]: () =>
			infoController.scraperProvinceGrainData(INFO_SOURCE.NMGLswz, true, 3)
	}
	const task = taskMap[source]

	if (!task) {
		return res.sendMessage('参数有误')
	}

	task()
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

module.exports = router
