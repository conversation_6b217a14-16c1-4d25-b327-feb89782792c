const express = require('express')
const router = express.Router()

const userController = require('../../controllers/userController')

router.post('/load', function (req, res) {
	//查询用户列表
	const { mobile, name, address, type, pageIndex, pageSize } = req.body
	console.log('%c [ req.body ] ', 'background:pink; color:#bf2c9f;', req.body)
	userController
		.queryUsers(mobile, name, address, type, pageIndex, pageSize)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.put('/update/:id', function (req, res) {
	// 更新用户类型
	const { id } = req.params
	if (id) {
		userController
			.updateInfo(id, req.body)
			.then(data => {
				res.sendSuccess(data)
			})
			.catch(err => {
				res.sendMessage(err)
			})
	} else {
		res.sendMessage('参数有误')
	}
})

module.exports = router
