-- 二维码管理表结构设计
-- 微信群二维码管理系统

CREATE TABLE `t_qrcode` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) DEFAULT NULL COMMENT '归属用户ID，为空表示系统级二维码',
  `user_name` varchar(50) DEFAULT NULL COMMENT '用户名（冗余字段，便于查询）',
  `user_mobile` varchar(20) DEFAULT NULL COMMENT '用户手机号（冗余字段，便于查询）',
  `user_type` int(11) DEFAULT NULL COMMENT '用户类型（冗余字段）',
  `qr_code_url` varchar(500) NOT NULL COMMENT '二维码图片URL',
  `description` text COMMENT '描述信息',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否生效：1-生效，0-不生效',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` int(11) DEFAULT NULL COMMENT '创建人ID',
  `update_by` int(11) DEFAULT NULL COMMENT '更新人ID',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_user_name` (`user_name`),
  KEY `idx_user_mobile` (`user_mobile`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信群二维码管理表';

-- 插入测试数据
INSERT INTO `t_qrcode` (`user_id`, `user_name`, `user_mobile`, `user_type`, `qr_code_url`, `description`, `is_active`, `create_by`) VALUES
(NULL, NULL, NULL, NULL, 'https://example.com/qrcode/system1.jpg', '系统级二维码 - 官方微信群', 1, 1),
(1, '张三', '13800138001', 1, 'https://example.com/qrcode/user1.jpg', '收购商张三的微信群二维码', 1, 1),
(2, '李四', '13800138002', 2, 'https://example.com/qrcode/user2.jpg', '粮庄李四的微信群二维码', 0, 1),
(3, '王五', '13800138003', 10, 'https://example.com/qrcode/user3.jpg', '信息收集员王五的微信群二维码', 1, 1);
