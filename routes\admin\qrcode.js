const express = require('express')
const router = express.Router()
const commonController = require('../../controllers/commonController')

const qrcodeController = require('../../controllers/qrcodeController')

/**
 * 查询二维码列表
 * POST /admin/qrcode/load
 */
router.post('/load', function (req, res) {
	const { userName, userMobile, isActive, pageIndex, pageSize } = req.body
	console.log('%c [ 二维码查询参数 ] ', 'background:pink; color:#bf2c9f;', req.body)

	qrcodeController
		.queryQrcodes(userName, userMobile, isActive, pageIndex, pageSize)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

/**
 * 创建二维码
 * POST /admin/qrcode/create
 */
router.post('/create', function (req, res) {
	const createBy = req.user ? req.user.id : 1 // 从登录用户获取，暂时默认为1

	qrcodeController
		.createQrcode(req.body, createBy)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

/**
 * 更新二维码
 * PUT /admin/qrcode/update/:id
 */
router.put('/update/:id', function (req, res) {
	const { id } = req.params
	const updateBy = req.user ? req.user.id : 1 // 从登录用户获取，暂时默认为1

	if (id) {
		qrcodeController
			.updateQrcode(id, req.body, updateBy)
			.then(data => {
				res.sendSuccess(data)
			})
			.catch(err => {
				res.sendMessage(err)
			})
	} else {
		res.sendMessage('参数有误')
	}
})

/**
 * 删除二维码
 * DELETE /admin/qrcode/delete/:id
 */
router.delete('/delete/:id', function (req, res) {
	const { id } = req.params

	if (id) {
		qrcodeController
			.deleteQrcode(id)
			.then(data => {
				res.sendSuccess(data)
			})
			.catch(err => {
				res.sendMessage(err)
			})
	} else {
		res.sendMessage('参数有误')
	}
})

/**
 * 获取二维码详情
 * GET /admin/qrcode/detail/:id
 */
router.get('/detail/:id', function (req, res) {
	const { id } = req.params

	if (id) {
		qrcodeController
			.getQrcodeDetail(id)
			.then(data => {
				res.sendSuccess(data)
			})
			.catch(err => {
				res.sendMessage(err)
			})
	} else {
		res.sendMessage('参数有误')
	}
})

/**
 * 获取所有用户列表（用于下拉选择）
 * GET /admin/qrcode/users
 */
router.get('/users', function (req, res) {
	qrcodeController
		.getAllUsers()
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

/**
 * 批量更新二维码状态
 * POST /admin/qrcode/batch-status
 */
router.post('/batch-status', function (req, res) {
	const { ids, isActive } = req.body
	const updateBy = req.user ? req.user.id : 1 // 从登录用户获取，暂时默认为1

	qrcodeController
		.batchUpdateQrcodeStatus(ids, isActive, updateBy)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

/**
 * 上传二维码图片到阿里云OSS
 * POST /admin/qrcode/upload
 */
router.post('/upload', async function (req, res) {
	try {
		// 二维码专用的上传配置
		const qrcodeUploadOptions = {
			maxFileSize: 2 * 1024 * 1024, // 2MB限制
			allowedMimeTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
			allowedExtensions: /\.(jpeg|jpg|png|gif)$/i,
			filePrefix: 'qrcode',
			errorMessage: '只允许上传图片文件 (jpeg, jpg, png, gif)'
		}

		// 使用通用的OSS上传功能，传入二维码专用配置
		const data = await commonController.uploadFileToOSS(req, 'qrcode', qrcodeUploadOptions)

		// 返回与原接口兼容的格式
		res.sendSuccess({
			url: data.url,
			filename: data.url.split('/').pop(), // 从URL中提取文件名
			originalname: data.fields.originalname || 'qrcode.jpg',
			size: data.fields.size || 0
		})
	} catch (error) {
		console.error('二维码图片上传到OSS失败:', error)
		res.sendMessage(error.message || '文件上传失败')
	}
})

module.exports = router
