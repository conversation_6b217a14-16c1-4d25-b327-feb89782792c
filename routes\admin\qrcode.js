const express = require('express')
const router = express.Router()
const multer = require('multer')
const path = require('path')
const fs = require('fs')

const qrcodeController = require('../../controllers/qrcodeController')

// 配置文件上传
const storage = multer.diskStorage({
	destination: function (req, file, cb) {
		const uploadPath = path.join(__dirname, '../../public/uploads/qrcode')
		// 确保目录存在
		if (!fs.existsSync(uploadPath)) {
			fs.mkdirSync(uploadPath, { recursive: true })
		}
		cb(null, uploadPath)
	},
	filename: function (req, file, cb) {
		// 生成唯一文件名
		const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9)
		const ext = path.extname(file.originalname)
		cb(null, 'qrcode-' + uniqueSuffix + ext)
	}
})

const upload = multer({
	storage: storage,
	limits: {
		fileSize: 2 * 1024 * 1024 // 限制2MB
	},
	fileFilter: function (req, file, cb) {
		// 检查文件类型
		const allowedTypes = /jpeg|jpg|png|gif/
		const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase())
		const mimetype = allowedTypes.test(file.mimetype)
		
		if (mimetype && extname) {
			return cb(null, true)
		} else {
			cb(new Error('只允许上传图片文件 (jpeg, jpg, png, gif)'))
		}
	}
})

/**
 * 查询二维码列表
 * POST /admin/qrcode/load
 */
router.post('/load', function (req, res) {
	const { userName, userMobile, isActive, pageIndex, pageSize } = req.body
	console.log('%c [ 二维码查询参数 ] ', 'background:pink; color:#bf2c9f;', req.body)
	
	qrcodeController
		.queryQrcodes(userName, userMobile, isActive, pageIndex, pageSize)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

/**
 * 创建二维码
 * POST /admin/qrcode/create
 */
router.post('/create', function (req, res) {
	const createBy = req.user ? req.user.id : 1 // 从登录用户获取，暂时默认为1
	
	qrcodeController
		.createQrcode(req.body, createBy)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

/**
 * 更新二维码
 * PUT /admin/qrcode/update/:id
 */
router.put('/update/:id', function (req, res) {
	const { id } = req.params
	const updateBy = req.user ? req.user.id : 1 // 从登录用户获取，暂时默认为1
	
	if (id) {
		qrcodeController
			.updateQrcode(id, req.body, updateBy)
			.then(data => {
				res.sendSuccess(data)
			})
			.catch(err => {
				res.sendMessage(err)
			})
	} else {
		res.sendMessage('参数有误')
	}
})

/**
 * 删除二维码
 * DELETE /admin/qrcode/delete/:id
 */
router.delete('/delete/:id', function (req, res) {
	const { id } = req.params
	
	if (id) {
		qrcodeController
			.deleteQrcode(id)
			.then(data => {
				res.sendSuccess(data)
			})
			.catch(err => {
				res.sendMessage(err)
			})
	} else {
		res.sendMessage('参数有误')
	}
})

/**
 * 获取二维码详情
 * GET /admin/qrcode/detail/:id
 */
router.get('/detail/:id', function (req, res) {
	const { id } = req.params
	
	if (id) {
		qrcodeController
			.getQrcodeDetail(id)
			.then(data => {
				res.sendSuccess(data)
			})
			.catch(err => {
				res.sendMessage(err)
			})
	} else {
		res.sendMessage('参数有误')
	}
})

/**
 * 获取所有用户列表（用于下拉选择）
 * GET /admin/qrcode/users
 */
router.get('/users', function (req, res) {
	qrcodeController
		.getAllUsers()
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

/**
 * 批量更新二维码状态
 * POST /admin/qrcode/batch-status
 */
router.post('/batch-status', function (req, res) {
	const { ids, isActive } = req.body
	const updateBy = req.user ? req.user.id : 1 // 从登录用户获取，暂时默认为1
	
	qrcodeController
		.batchUpdateQrcodeStatus(ids, isActive, updateBy)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

/**
 * 上传二维码图片
 * POST /admin/qrcode/upload
 */
router.post('/upload', upload.single('file'), function (req, res) {
	try {
		if (!req.file) {
			return res.sendMessage('请选择要上传的文件')
		}
		
		// 构建文件访问URL
		const fileUrl = `/uploads/qrcode/${req.file.filename}`
		
		res.sendSuccess({
			url: fileUrl,
			filename: req.file.filename,
			originalname: req.file.originalname,
			size: req.file.size
		})
	} catch (error) {
		console.error('文件上传错误:', error)
		res.sendMessage('文件上传失败')
	}
})

// 错误处理中间件
router.use((error, req, res, next) => {
	if (error instanceof multer.MulterError) {
		if (error.code === 'LIMIT_FILE_SIZE') {
			return res.sendMessage('文件大小不能超过2MB')
		}
	}
	if (error.message) {
		return res.sendMessage(error.message)
	}
	res.sendMessage('上传失败')
})

module.exports = router
