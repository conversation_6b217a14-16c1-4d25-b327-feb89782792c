import http from '@/utils/http'

function queryList(params) {
	console.log('%c [ params ] ', 'font-size:13px; background:pink; color:#bf2c9f;', params)
	return http.post('admin/transport/list', params)
}

function addFactory(params) {
	return http.post('admin/transport/add', params)
}

function updateFactory(id, params) {
	return http.put('admin/transport/edit/' + id, params)
}

function deleteFactory(id) {
	return http.delete('admin/transport/delete/' + id)
}
const getHistoryRecords = id => {
	return http.get(`admin/transport/records/${id}`)
}

export default {
	queryList,
	addFactory,
	updateFactory,
	deleteFactory,
	getHistoryRecords
}
