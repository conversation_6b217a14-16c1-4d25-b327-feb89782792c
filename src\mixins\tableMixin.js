// 每页表格拉取数据
export default {
    data () {
        return {
            loading: true,
            tableData: [],
            total: 0,
            pageIndex: 0,
            pageSize: 10
        }
    },
    created () {
        this.loadData();
    },
    methods: {
        getCropName (row) {
            const { cropId, cropChildId } = row;
            const id = cropChildId || cropId;
            const crop = this.$store.state.cropMap[id];
            if (crop) {
                return crop.name;
            } else {
                return '作物_' + id
            }
        }
    }
}