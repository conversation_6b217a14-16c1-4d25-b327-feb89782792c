<template>
<!-- Navbar -->
<nav class="main-header navbar navbar-expand navbar-white navbar-light">
    <!-- Left navbar links -->
    <ul class="navbar-nav">
		<li class="nav-item">
			<a class="nav-link" data-widget="pushmenu" href="#"><i class="fas fa-bars"></i></a>
		</li>
		<li class="nav-item d-none d-sm-inline-block">
			<router-link to="/order/calling" class="nav-link">订单列表</router-link>
		</li>
    </ul>

    <!-- Right navbar links -->
	<ul class="navbar-nav ml-auto">
		<li class="dropdown user user-menu">
			<a class="nav-link" data-toggle="dropdown" href="#">
				<img src="../../images/user.jpg" class="user-image" alt="user">
				<span class="hidden-xs">{{ admin.username }}</span>
			</a>
			<ul class="dropdown-menu dropdown-menu-lg dropdown-menu-right">
				<li class="user-header">
					<img src="../../images/user.jpg" class="img-circle" alt="user">
					<p>
						{{ admin.username }} - 系统管理员 <small>最近登录 {{ loginTime }}</small>
					</p>
				</li>
				<li class="user-footer">
					<div class="float-left">
						<router-link to="/setting/password" class="btn btn-default btn-flat">修改密码</router-link>
					</div>
					<div class="float-right">
						<a @click="logout()" class="btn btn-default btn-flat" style="cursor:pointer">注 销</a>
					</div>
				</li>
			</ul>
		</li>
	</ul>
</nav>
  <!-- /.navbar -->
</template>

<script>

import userManager from '@/manager/userManager'
import orderManager from '@/manager/orderManager'

export default {
	data(){
		return {
			loginTime: ''
		}
	},
	computed: {
		admin(){
			return this.$store.state.admin || {}
		}
	},
	created(){
		let loginTime = userManager.getLoginTime();
		if(loginTime){
			this.loginTime = new Date(loginTime).format('yyyy-MM-dd hh:mm:ss');
		}
		// this.refresh();
	},
	methods:{
		startRefreshTimer(){
			//TO DO
		},
		logout(){
			this.$confirm('确认要注销当前账号吗？').then(() => {
				userManager.logout().then(() => {
					this.$store.commit('CLEAR_ADMIN')
					this.$router.push('/login')
				}).catch(err => {
					this.$toast.error(err);
				})
			}).catch(() => {
				this.$toast('已取消操作', 1000);
			})
		},
		search(){
			console.log(this.searchValue)
		},
		refresh(){
			orderManager.refreshMessages().then(data => {
				console.log(data);
			}).catch(err => {
				this.$toast.error(err);
			})
		}
	}
}
</script>