const fsp = require('fs').promises
const path = require('path')
const formidable = require('formidable')
const { getUuid } = require('../utils/common')
const ossController = require('./ossController')

/**
 * 处理本地文件上传的通用函数
 * @param {import('http').IncomingMessage} req - HTTP请求对象
 * @param {string} subDir - 文件存储的子目录 (e.g., 'images', 'videos')
 * @returns {Promise<string>} 返回文件的相对路径
 */
async function _handleLocalUpload(req, subDir) {
	const uploadDir = path.join(__dirname, '../public/upload/', subDir)
	await fsp.mkdir(uploadDir, { recursive: true })

	const form = formidable({
		uploadDir: uploadDir,
		keepExtensions: true
	})

	const { files } = await new Promise((resolve, reject) => {
		form.parse(req, (err, fields, files) => {
			if (err) {
				return reject(new Error(`文件解析失败: ${err.message}`))
			}
			resolve({ fields, files })
		})
	})

	const uploadedFile = files.file && files.file[0]
	if (!uploadedFile) {
		throw new Error('未检测到有效文件上传')
	}

	const tempFilePath = uploadedFile.filepath
	try {
		const ext = path.extname(uploadedFile.originalFilename)
		const newName = `order_${getUuid(12)}${ext}`
		const newPath = path.join(uploadDir, newName)

		await fsp.rename(tempFilePath, newPath)
		return `/upload/${subDir}/${newName}`
	} catch (renameErr) {
		// 如果重命名失败，尝试删除临时文件
		try {
			await fsp.unlink(tempFilePath)
		} catch (unlinkErr) {
			console.error('临时文件删除失败:', tempFilePath, unlinkErr)
		}
		// 在 async 函数中，直接抛出错误
		throw new Error(`文件保存失败: ${renameErr.message}`)
	}
}

/**
 * 上传图片文件
 * @param {import('http').IncomingMessage} req - HTTP请求对象
 */
function uploadPhoto(req) {
	return _handleLocalUpload(req, 'images')
}

/**
 * 上传视频文件
 * @param {import('http').IncomingMessage} req - HTTP请求对象
 */
function uploadVideo(req) {
	return _handleLocalUpload(req, 'videos')
}

/**
 * 通用文件上传并转存到OSS。
 * 文件将上传到OSS的指定目录中。目录可以通过两种方式指定，优先级从高到低：
 * 1. 客户端在 multipart/form-data 请求中提供一个名为 'namespace' 的字段。
 * 2. 调用此函数时传入 `defaultNamespace` 参数。
 *
 * @param {import('http').IncomingMessage} req - HTTP请求对象，包含文件数据。
 * @param {string} [defaultNamespace='common'] - 如果客户端未指定，则使用的默认OSS存储目录。
 * @returns {Promise<{url: string, fields: object}>} 返回包含OSS URL和原始表单字段的对象。
 */
async function uploadFileToOSS(req, defaultNamespace = 'common') {
	const tempDir = path.join(__dirname, '../temp')
	await fsp.mkdir(tempDir, { recursive: true })

	const form = formidable({
		uploadDir: tempDir,
		keepExtensions: true,
		maxFileSize: 100 * 1024 * 1024, // 100MB
		filename: (name, ext) => `${name}-${Date.now()}${ext}`
	})

	// 监听进度事件
	form.on('progress', (bytesReceived, bytesExpected) => {
		if (bytesExpected > 0) {
			const percent = ((bytesReceived / bytesExpected) * 100).toFixed(2)
			console.log(`文件上传进度: ${percent}%`)
		}
	})

	const { fields, files } = await new Promise((resolve, reject) => {
		form.parse(req, (err, fields, files) => {
			if (err) {
				return reject(new Error(`文件解析失败: ${err.message}`))
			}
			resolve({ fields, files })
		})
	})
	// 动态查找第一个上传的文件，而不是硬编码 'file' 字段。
	// 这使得接口更健壮，能适应不同的前端字段名（如 'image', 'upload' 等）。
	const allFiles = Object.values(files).flat()
	const uploadedFile = allFiles[0]

	if (!uploadedFile) {
		throw new Error('未检测到有效文件上传')
	}

	const tempFilePath = uploadedFile.filepath
	try {
		// 确认临时文件存在
		await fsp.access(tempFilePath)

		// 优先使用客户端在表单中定义的 'namespace' 字段作为分类
		const uploadNamespace =
			fields.namespace && fields.namespace[0] ? fields.namespace[0] : defaultNamespace

		const ossUrl = await ossController.uploadToOSS(tempFilePath, uploadNamespace)

		return { url: ossUrl, fields }
	} catch (uploadErr) {
		// 在 async 函数中，直接抛出错误
		throw new Error(`文件处理或上传OSS失败: ${uploadErr.message}`)
	} finally {
		// 总是尝试清理临时文件
		try {
			await fsp.unlink(tempFilePath)
		} catch (unlinkErr) {
			console.error('临时文件删除失败:', tempFilePath, unlinkErr)
		}
	}
}

module.exports = {
	uploadPhoto,
	uploadVideo,
	uploadFileToOSS
}
