<template>
    <div class="navbar_root" :class="{ sidebar_collapse: appStore.isMenuCollapsed }">
        <span class="navbar_item navbar_btn" @click="appStore.toggleSidebarCollapse()">
            <el-icon><Fold /></el-icon>
        </span>
        <span class="navbar_item navbar_submenu">{{ submenu }}</span>
        <span class="navbar_item navbar_logout fr" @click="logout">退出</span>
        <span class="navbar_item user_name fr">{{ authStore.admin.username }}</span>
        <span class="navbar_item user_photo fr">
            <img :src="authStore.admin.avatar" alt="nongzhou">
        </span>
    </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Fold } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { useAppStore } from '@/stores/app'
import contextManager from '@/manager/contextManager'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const appStore = useAppStore()

const submenu = computed(() => {
    return route.matched.map(item => item.meta?.title).filter(Boolean).join(' / ')
})

const logout = async () => {
    await contextManager.logout()
    authStore.logout()
    router.push('/login')
}
</script>

<style lang="scss">
.navbar_root{
    width: 100%;
    height: 56px;
    line-height: 55px;
    padding-left: 180px;
    border-bottom: 1px solid #e1e1e1;
    background: #f4f6f9;
    box-sizing: border-box;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 99;
    .navbar_item{
        display: inline-block;
        vertical-align: middle;
    }
    .navbar_btn{
        font-size: 24px;
        width: 60px;
        text-align: center;
        cursor: pointer;
    }
    .user_name{
        font-size: 18px;
        margin-right: 20px;
    }
    .user_photo{
        width: 44px;
        height: 44px;
        margin: 6px 10px;
        img{
            display: block;
            width: 100%;
        }
        border-radius: 25px;
        overflow: hidden;
    }
    .navbar_submenu{
        display: inline-block;
        margin-left: 10px;
        font-size: 16px;
    }
    .navbar_logout{
        font-size: 16px;
        color: #999;
        padding: 0 20px;
        cursor: pointer;
        &:hover{
            color: #333333;
        }
    }
    &.sidebar_collapse{
        padding-left: 64px;
        .navbar_btn{
            transform: rotate(-180deg);
        }
    }
}
</style>