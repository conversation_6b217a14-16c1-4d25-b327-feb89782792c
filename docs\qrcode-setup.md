# 二维码管理功能部署指南

## 概述

本文档描述了如何部署和配置微信群二维码管理功能的后端服务。

## 前置条件

1. Node.js 环境
2. MySQL 数据库
3. 已有的后端项目框架

## 部署步骤

### 1. 数据库初始化

执行SQL脚本创建二维码表：

```bash
mysql -u username -p database_name < sql/qrcode.sql
```

或者在MySQL客户端中执行 `sql/qrcode.sql` 文件中的SQL语句。

### 2. 安装依赖

如果还没有安装multer依赖，请运行：

```bash
npm install multer
```

### 3. 创建上传目录

确保以下目录存在并有写入权限：

```bash
mkdir -p public/uploads/qrcode
chmod 755 public/uploads/qrcode
```

### 4. 配置静态文件服务

在主应用文件（如 `app.js`）中确保已配置静态文件服务：

```javascript
app.use('/uploads', express.static(path.join(__dirname, 'public/uploads')))
```

### 5. 验证路由注册

确认 `routes/admin/index.js` 中已正确注册二维码路由：

```javascript
const qrcodeRouter = require('./qrcode')
router.use('/qrcode', qrcodeRouter)
```

### 6. 重启服务

重启Node.js服务以加载新的路由和控制器：

```bash
npm run dev  # 开发环境
# 或
npm start    # 生产环境
```

## 功能验证

### 1. 检查API端点

访问以下URL验证API是否正常工作：

- `GET /api/v1/admin/qrcode/users` - 获取用户列表
- `POST /api/v1/admin/qrcode/load` - 查询二维码列表

### 2. 运行测试脚本

```bash
node test/qrcode-api-test.js
```

### 3. 前端集成测试

确保前端的 `qrcodeManager.js` 能正常调用后端API。

## 配置说明

### 文件上传配置

在 `routes/admin/qrcode.js` 中可以调整以下配置：

```javascript
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 2 * 1024 * 1024 // 文件大小限制：2MB
  },
  fileFilter: function (req, file, cb) {
    // 支持的文件类型
    const allowedTypes = /jpeg|jpg|png|gif/
    // ...
  }
})
```

### 数据库字段说明

| 字段 | 类型 | 说明 | 是否必填 |
|------|------|------|----------|
| user_id | int | 归属用户ID | 否 |
| qr_code_url | varchar(500) | 二维码图片URL | 是 |
| description | text | 描述信息 | 否 |
| is_active | tinyint(1) | 是否生效 | 是 |

## 权限控制

### 管理员认证

所有二维码管理API都需要管理员权限。确保在路由中添加适当的认证中间件：

```javascript
// 在 routes/admin/index.js 或具体路由文件中
router.use(adminAuthMiddleware)
```

### 操作日志

建议在关键操作（创建、更新、删除）中记录操作日志：

```javascript
// 在控制器方法中添加日志记录
logController.recordOperation({
  userId: req.user.id,
  action: 'CREATE_QRCODE',
  target: 'qrcode',
  targetId: newQrcodeId
})
```

## 故障排除

### 常见问题

1. **文件上传失败**
   - 检查 `public/uploads/qrcode` 目录是否存在
   - 检查目录权限是否正确
   - 确认multer依赖已安装

2. **数据库连接错误**
   - 检查数据库配置
   - 确认 `t_qrcode` 表已创建
   - 检查数据库用户权限

3. **路由404错误**
   - 确认路由已正确注册
   - 检查URL路径是否正确
   - 验证中间件配置

### 调试方法

1. 启用详细日志：
```javascript
console.log('%c [ 二维码查询参数 ] ', 'background:pink; color:#bf2c9f;', req.body)
```

2. 检查数据库查询：
```javascript
// 在dbController中添加SQL日志
console.log('执行SQL:', sql, params)
```

3. 验证文件上传：
```javascript
// 在上传路由中添加调试信息
console.log('上传文件信息:', req.file)
```

## 性能优化

### 数据库优化

1. 为常用查询字段添加索引：
```sql
CREATE INDEX idx_user_name ON t_qrcode(user_name);
CREATE INDEX idx_user_mobile ON t_qrcode(user_mobile);
CREATE INDEX idx_is_active ON t_qrcode(is_active);
```

2. 定期清理无效的二维码记录

### 文件存储优化

1. 考虑使用CDN存储二维码图片
2. 实现图片压缩和格式转换
3. 定期清理未使用的图片文件

## 扩展功能

### 可选增强功能

1. **二维码生成**：集成二维码生成库
2. **图片处理**：自动压缩和格式转换
3. **批量导入**：支持Excel批量导入二维码
4. **统计分析**：二维码使用情况统计
5. **定时任务**：定期检查二维码有效性

### 集成建议

1. 与用户管理系统深度集成
2. 添加二维码扫描统计功能
3. 实现二维码分组管理
4. 支持二维码模板功能

## 安全注意事项

1. 文件上传安全检查
2. SQL注入防护
3. 权限验证
4. 输入参数验证
5. 文件类型和大小限制
