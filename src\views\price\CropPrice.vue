<template>
    <div class="card">
        <div class="search_wrapper">
            <el-input v-model="searchParams.companyName" placeholder="收购厂名称"></el-input>
            <el-date-picker v-model="searchParams.date" type="date" value-format="yyyy-MM-dd" placeholder="选择日期"></el-date-picker>
            <el-select style="width: 140px" v-model="searchParams.cropId" placeholder="请选择作物">
                <el-option v-for="item in cropArr" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
            <el-button type="primary" @click="loadData">查询</el-button>
            <el-button @click="clear">清空</el-button>
        </div>
        <div class="card-body">
            <el-table v-loading="loading" :data="tableData" border fit highlight-current-row style="width:100%">
                <el-table-column label="ID" prop="id" align="center" width="50"></el-table-column>
                <el-table-column label="收购公司ID" prop="companyId" align="center" width="100"></el-table-column>
                <el-table-column label="收购公司" prop="companyName" align="center" width="120"></el-table-column>
                <el-table-column label="收购作物" prop="cropId" align="center" width="100">
                    <template slot-scope="{ row }">{{ getCropName(row) }}</template>
                </el-table-column>
                <el-table-column label="价格" prop="price" align="center"></el-table-column>
                <el-table-column label="对比昨日" prop="minusYesterday" align="center">
                    <template slot-scope="{ row }">
                        <span v-if="row.minusYesterday > 0" style="color: red">{{row.minusYesterday}}</span>
                        <span v-else-if="row.minusYesterday < 0" style="color: green">{{row.minusYesterday}}</span>
                        <span v-else>{{row.minusYesterday}}</span>
                    </template>
                </el-table-column>
                <el-table-column label="报价日期" prop="date" align="center"></el-table-column>
                <el-table-column label="创建时间" prop="createTime" align="center" width="95">
                    <template slot-scope="{ row }">{{ new Date(row.createTime).format('yyyy-MM-dd hh:mm:ss') }}</template>
                </el-table-column>
                <el-table-column label="操作" align="center">
                    <template slot-scope="{ row }">
                        <el-button size="mini" @click="showEdit(row)">编辑</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <Pagination v-if="total > 0" :total="total" :pageIndex.sync="pageIndex" :pageSize.sync="pageSize" @pagination="loadData" />
        </div>
        <el-dialog title="修改报价信息" :visible.sync="priceDialogVisible">
            <el-form label-width="110px">
                <el-form-item label="收购公司">
                    <el-input :value="editPriceItem.companyName" readonly></el-input>
                </el-form-item>
                <el-form-item label="收购作物">
                    <el-select v-model="editPriceItem.cropId" placeholder="请选择作物">
                        <el-option v-for="item in cropArr" :key="item.id" :label="item.name" :value="item.id"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item :label="`报价（${editPriceItem.unit}）`">
                    <el-input-number v-model="editPriceItem.price" :precision="4" :step="0.1"></el-input-number>
                </el-form-item>
                <el-form-item label="报价日期">
                    <el-date-picker v-model="editPriceItem.date" type="date" value-format="yyyy-MM-dd" placeholder="选择日期" disabled></el-date-picker>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="priceDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="onEditPriceBtnConfirm">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
// import companyManager from '@/manager/companyManager'
import cropPriceManager from '@/manager/cropPriceManager'
import Pagination from '@/components/Pagination'
import tableMixin from '@/mixins/tableMixin'
export default {
    name: 'CompanyList',
    data () {
        return {
            priceDialogVisible: false,
            searchParams: {
                companyName: '',
                date: null,
                cropId: null
            },
            cropArr: [
                { id: 1, name: '玉米' },
                { id: 2, name: '小麦' }
            ],
            editPriceItem: {}
        }
    },
    components: { Pagination },
    mixins: [tableMixin],
    methods: {
        loadData () {
            const { searchParams: { companyName, cropId, date }, pageIndex, pageSize } = this;
            this.loading = true;
            cropPriceManager.queryAllPrices(companyName, cropId, date, pageIndex, pageSize).then(res => {
                const { total, list } = res;
                this.total = total;
                this.tableData = list.map(item => {
                    item.date = new Date(item.date).format('yyyy-MM-dd');
                    return item;
                });
            }).catch(err => {
                this.$message.error(err)
            }).then(() => {
                this.loading = false;
            })
        },
        clear () {
            this.searchParams = {
                companyName: '',
                date: null,
                cropId: null
            }
        },
        showEdit (item) {
            if (item.date >= new Date().format('yyyy-MM-dd')) {
                this.editPriceItem = Object.assign({}, item);
                this.priceDialogVisible = true;
            } else {
                this.$alert('暂不允许编辑往日的价格', '提示', { type: 'error' })
            }
        },
        onEditPriceBtnConfirm () {
            const { id, cropId, companyId, price, date } = this.editPriceItem;
            if (price) {
                cropPriceManager.updatePrice(id, cropId, companyId, price, date).then(() => {
                    this.$message.success('更新成功')
                    this.priceDialogVisible = false;
                    this.loadData();
                }).catch(err => {
                    this.$message.error(err)
                })
            } else {
                this.$alert('请输入价格', '提示', { type: 'error' })
            }
        }
    }
}
</script>