<template>
	<div class="wrapper">
		<!-- Main Header -->
		<Navbar />

		<!-- Left side column. contains the logo and sidebar -->
		<MainSidebar />

		<!-- Content Wrapper. Contains page content -->
		<MainApp />
		<!-- /.content-wrapper -->

		<!-- Main Footer -->
		<Footer />

		<!-- Control Sidebar -->
		<ControlSidebar />
	</div>
</template>

<script>
import Navbar from './modules/Navbar'
import MainSidebar from './modules/MainSidebar'
import MainApp from './modules/MainApp'
import Footer from './modules/Footer'
import ControlSidebar from './modules/ControlSidebar'
export default {
    components: {
        Navbar,
        MainSidebar,
        MainApp,
        Footer,
        ControlSidebar
    }
}
</script>