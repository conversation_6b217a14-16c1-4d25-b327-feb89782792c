# 部署脚本会在部署组的每台机器上执行。一个典型脚本逻辑如下：
# 先将制品包（在下载路径中配置的下载路径）解压缩到指定目录中，
# 再执行启动脚本（通常在代码中维护，如示例中deploy.sh）。关于这个例子的详细解释见 https://help.aliyun.com/document_detail/153848.html \n\n 
# tar zxvf /home/<USER>/app/package.tgz -C /home/<USER>/app/\n 
# sh /home/<USER>/app/deploy.sh restart\n 
# 如果你是php之类的无需制品包的制品方式，可以使用 git clone 或者 git pull将源代码更新到服务器，再执行其他命令 \n # git clone ***@***.git\n


# sh /home/<USER>/app/deploy.sh

# /home/<USER>/sh/admin-fontend.sh