/**
 * 二维码API测试脚本
 * 
 * 使用方法：
 * 1. 确保服务器已启动
 * 2. 运行 node test/qrcode-api-test.js
 */

const axios = require('axios')
const fs = require('fs')
const FormData = require('form-data')
const path = require('path')

// 配置
const API_BASE_URL = 'http://localhost:3000/api/v1'
const ADMIN_TOKEN = 'your_admin_token_here' // 替换为实际的管理员token

// 创建axios实例
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${ADMIN_TOKEN}`
  }
})

// 测试函数
async function runTests() {
  console.log('开始测试二维码API...')
  
  try {
    // 测试1: 查询二维码列表
    console.log('\n测试1: 查询二维码列表')
    const listResponse = await api.post('/admin/qrcode/load', {
      pageIndex: 1,
      pageSize: 10
    })
    console.log('状态码:', listResponse.status)
    console.log('数据总数:', listResponse.data.data.total)
    console.log('列表条数:', listResponse.data.data.list.length)
    
    // 测试2: 创建二维码
    console.log('\n测试2: 创建二维码')
    const createResponse = await api.post('/admin/qrcode/create', {
      qrCodeUrl: 'https://example.com/test-qrcode.jpg',
      description: '测试二维码',
      isActive: 1
    })
    console.log('状态码:', createResponse.status)
    console.log('消息:', createResponse.data.message)
    
    // 获取最新创建的二维码ID
    const newListResponse = await api.post('/admin/qrcode/load', {
      pageIndex: 1,
      pageSize: 1
    })
    const newQrcodeId = newListResponse.data.data.list[0].id
    console.log('新创建的二维码ID:', newQrcodeId)
    
    // 测试3: 获取二维码详情
    console.log('\n测试3: 获取二维码详情')
    const detailResponse = await api.get(`/admin/qrcode/detail/${newQrcodeId}`)
    console.log('状态码:', detailResponse.status)
    console.log('二维码详情:', detailResponse.data.data)
    
    // 测试4: 更新二维码
    console.log('\n测试4: 更新二维码')
    const updateResponse = await api.put(`/admin/qrcode/update/${newQrcodeId}`, {
      description: '已更新的测试二维码',
      isActive: 0
    })
    console.log('状态码:', updateResponse.status)
    console.log('消息:', updateResponse.data.message)
    
    // 测试5: 获取用户列表
    console.log('\n测试5: 获取用户列表')
    const usersResponse = await api.get('/admin/qrcode/users')
    console.log('状态码:', usersResponse.status)
    console.log('用户数量:', usersResponse.data.data.length)
    
    // 测试6: 批量更新状态
    console.log('\n测试6: 批量更新状态')
    const batchResponse = await api.post('/admin/qrcode/batch-status', {
      ids: [newQrcodeId],
      isActive: 1
    })
    console.log('状态码:', batchResponse.status)
    console.log('消息:', batchResponse.data.message)
    
    // 测试7: 上传图片（需要实际图片文件）
    /*
    console.log('\n测试7: 上传图片')
    const formData = new FormData()
    const filePath = path.join(__dirname, 'test-qrcode.jpg')
    
    if (fs.existsSync(filePath)) {
      formData.append('file', fs.createReadStream(filePath))
      
      const uploadResponse = await axios.post(
        `${API_BASE_URL}/admin/qrcode/upload`,
        formData,
        {
          headers: {
            ...formData.getHeaders(),
            'Authorization': `Bearer ${ADMIN_TOKEN}`
          }
        }
      )
      
      console.log('状态码:', uploadResponse.status)
      console.log('上传结果:', uploadResponse.data.data)
    } else {
      console.log('测试图片文件不存在，跳过上传测试')
    }
    */
    
    // 测试8: 删除二维码
    console.log('\n测试8: 删除二维码')
    const deleteResponse = await api.delete(`/admin/qrcode/delete/${newQrcodeId}`)
    console.log('状态码:', deleteResponse.status)
    console.log('消息:', deleteResponse.data.message)
    
    console.log('\n所有测试完成!')
    
  } catch (error) {
    console.error('测试过程中出错:', error.message)
    if (error.response) {
      console.error('错误响应:', error.response.data)
    }
  }
}

// 运行测试
runTests()
