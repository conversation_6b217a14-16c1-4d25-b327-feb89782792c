# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Development
- `npm run dev` - Start development server with hot reload (opens browser automatically)
- `npm run build` - Build for production (outputs to `dist/` directory)
- `npm run lint` - Run ESLint to check and fix code style issues

### Project Setup
- `npm install` - Install all dependencies

## Architecture Overview

This is a Vue 2 admin dashboard application for managing agricultural crop pricing and trading information. The application follows a traditional Vue.js structure with a custom manager pattern for API interactions.

### Core Structure

**Entry Point (`src/main.js`)**
- Initializes Vue with Element UI components
- Loads essential data (cities, crops, user info) before app startup via Promise.all
- Uses custom date formatting utilities

**Manager Pattern (`src/manager/`)**
- Custom service layer handling all API communications
- Each manager corresponds to a business domain (user, crop, order, etc.)
- Managers use a centralized HTTP utility (`src/utils/http.js`) with automatic error handling
- Key managers:
  - `userManager.js` - Authentication and user operations
  - `cropManager.js` - Crop and region management
  - `orderManager.js` - Order processing
  - `companyManager.js` - Company and geographic data

**HTTP Layer (`src/utils/http.js`)**
- Centralized axios wrapper with automatic error handling
- Automatic token management and logout on authentication errors
- Standardized API response format handling
- All requests prefixed with `/api/v1/`

**State Management (`src/store/index.js`)**
- Simple Vuex store managing:
  - Authentication state (`logged`, `admin`)
  - UI state (`collapseMenu`)
  - Application data (`cropArr`, `cropMap`, `areaArr`)

**Routing (`src/router/index.js`)**
- Flat route structure with menu organization via metadata
- Routes grouped by functional areas:
  - System settings (crops, regions, config)
  - Price management (companies, pricing, ports)
  - Content management (orders, info, users)
- Uses submenu metadata for navigation grouping

### Key Features

**Authentication Flow**
- Login required for all routes except `/login`
- Token-based authentication with automatic logout on expiry
- User info loaded at app startup and stored in Vuex

**Layout System**
- Collapsible sidebar with dynamic margin adjustment
- Fixed navbar with responsive design
- Login/main app conditional rendering

**Development Proxy**
- Development server proxies to `http://localhost:3000`
- Production builds output to custom `admin/index.html` path

### Component Organization

**Views Structure**
- Organized by business domain in `src/views/`
- Complex views may have sub-components in dedicated folders
- Recent addition: `qrcode/` module with component-based architecture

**Mixins (`src/mixins/`)**
- `areaMixin.js` - Geographic area utilities
- `tableMixin.js` - Common table functionality
- `userMixin.js` - User-related utilities

**Utilities (`src/utils/`)**
- `common.js` - General utility functions  
- `date.js` - Date formatting extensions
- `messageBox.js` - Element UI message utilities
- `storeManager.js` - Local storage management

### Development Notes

**Styling**
- Uses SCSS with Element UI theme
- Main styles in `src/styles/app.scss`
- Element UI customizations in dedicated reset files

**Build Configuration**
- Vue CLI 4 based setup
- Production builds disable source maps
- Custom index path for production deployment
- Development server auto-opens browser

**Code Patterns**
- Manager functions return promises for async operations
- Consistent error handling through HTTP utility
- Component-based architecture for complex features
- Vuex mutations for state updates, no actions currently used