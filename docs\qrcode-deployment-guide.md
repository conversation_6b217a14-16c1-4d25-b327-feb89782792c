# 二维码管理功能完整部署指南

## 项目概述

本项目为微信群二维码管理系统，包含完整的前后端实现：
- 前端：Vue.js + Element UI 管理界面
- 后端：Node.js + Express + MySQL API服务

## 已完成的功能

### 后端API
✅ 二维码列表查询（支持分页和筛选）
✅ 二维码创建（支持系统级和用户级）
✅ 二维码更新（支持部分字段更新）
✅ 二维码删除
✅ 二维码详情查询
✅ 用户列表获取（用于下拉选择）
✅ 批量状态更新
✅ 图片文件上传（支持格式验证和大小限制）

### 前端界面
✅ 二维码列表展示（表格形式）
✅ 搜索筛选功能
✅ 新增二维码弹窗
✅ 编辑二维码弹窗
✅ 图片上传组件
✅ 状态切换操作
✅ 删除确认操作

### 数据库设计
✅ t_qrcode 表结构设计
✅ 索引优化
✅ 测试数据

## 部署步骤

### 1. 数据库初始化

```sql
-- 执行数据库脚本
source sql/qrcode.sql;

-- 或者直接执行SQL
mysql -u username -p database_name < sql/qrcode.sql
```

### 2. 后端部署

#### 安装依赖
```bash
npm install multer
```

#### 创建上传目录
```bash
mkdir -p public/uploads/qrcode
chmod 755 public/uploads/qrcode
```

#### 验证路由注册
确认以下文件已正确配置：
- ✅ `controllers/qrcodeController.js` - 业务逻辑控制器
- ✅ `routes/admin/qrcode.js` - 路由定义
- ✅ `routes/admin/index.js` - 路由注册
- ✅ `db.js` - 数据表名配置

#### 重启服务
```bash
npm run dev  # 开发环境
# 或
npm start    # 生产环境
```

### 3. 前端部署

#### 验证配置
确认以下文件已正确配置：
- ✅ `src/manager/qrcodeManager.js` - API调用管理器
- ✅ `src/views/qrcode/List.vue` - 主列表页面
- ✅ `src/views/qrcode/components/AddDialog.vue` - 新增弹窗（上传地址已修正）
- ✅ `src/views/qrcode/components/EditDialog.vue` - 编辑弹窗
- ✅ `src/views/qrcode/components/QrcodeTable.vue` - 表格组件
- ✅ `src/views/qrcode/components/SearchForm.vue` - 搜索组件

#### 构建部署
```bash
npm run build
```

## API接口测试

### 基础测试命令

```bash
# 1. 查询二维码列表
curl -X POST http://localhost:3000/api/v1/admin/qrcode/load \
  -H "Content-Type: application/json" \
  -d '{"pageIndex":1,"pageSize":10}'

# 2. 创建二维码
curl -X POST http://localhost:3000/api/v1/admin/qrcode/create \
  -H "Content-Type: application/json" \
  -d '{"qrCodeUrl":"https://example.com/test.jpg","description":"测试二维码","isActive":1}'

# 3. 获取用户列表
curl -X GET http://localhost:3000/api/v1/admin/qrcode/users

# 4. 上传图片测试
curl -X POST http://localhost:3000/api/v1/admin/qrcode/upload \
  -F "file=@/path/to/your/image.jpg"
```

### 自动化测试

运行测试脚本：
```bash
node test/qrcode-api-test.js
```

## 功能验证清单

### 后端API验证
- [ ] 二维码列表查询正常返回
- [ ] 分页参数正确处理
- [ ] 搜索筛选功能正常
- [ ] 二维码创建成功
- [ ] 用户信息自动关联
- [ ] 二维码更新功能正常
- [ ] 二维码删除功能正常
- [ ] 文件上传功能正常
- [ ] 文件格式验证有效
- [ ] 文件大小限制有效
- [ ] 批量状态更新正常

### 前端界面验证
- [ ] 二维码列表正常显示
- [ ] 搜索功能正常工作
- [ ] 新增弹窗正常打开
- [ ] 图片上传功能正常
- [ ] 用户选择功能正常
- [ ] 编辑弹窗正常打开
- [ ] 状态切换功能正常
- [ ] 删除确认功能正常
- [ ] 分页功能正常

### 数据一致性验证
- [ ] 前后端数据格式匹配
- [ ] 用户信息正确冗余
- [ ] 时间格式正确显示
- [ ] 状态值正确映射

## 常见问题解决

### 1. 文件上传失败
**问题**: 上传图片时返回404或500错误
**解决方案**:
```bash
# 检查目录是否存在
ls -la public/uploads/qrcode

# 检查权限
chmod 755 public/uploads/qrcode

# 检查multer依赖
npm list multer
```

### 2. 路由404错误
**问题**: API调用返回404
**解决方案**:
```javascript
// 检查路由注册
console.log('二维码路由已注册:', require('./routes/admin/qrcode'))

// 检查URL路径
console.log('请求URL:', req.originalUrl)
```

### 3. 数据库连接错误
**问题**: 查询时返回数据库错误
**解决方案**:
```sql
-- 检查表是否存在
SHOW TABLES LIKE 't_qrcode';

-- 检查表结构
DESCRIBE t_qrcode;

-- 检查数据
SELECT COUNT(*) FROM t_qrcode;
```

### 4. 前端调用失败
**问题**: 前端无法调用后端API
**解决方案**:
```javascript
// 检查代理配置
// vue.config.js
devServer: {
  proxy: 'http://localhost:3000'
}

// 检查API基础路径
// src/utils/http.js
url = `/api/v1/${url}`;
```

## 性能优化建议

### 数据库优化
```sql
-- 添加复合索引
CREATE INDEX idx_user_active ON t_qrcode(user_id, is_active);
CREATE INDEX idx_create_time_desc ON t_qrcode(create_time DESC);
```

### 文件存储优化
```javascript
// 考虑使用CDN
const uploadPath = process.env.CDN_UPLOAD_PATH || path.join(__dirname, '../../public/uploads/qrcode')

// 图片压缩
const sharp = require('sharp')
// 在上传处理中添加压缩逻辑
```

### 缓存优化
```javascript
// 用户列表缓存
const NodeCache = require('node-cache')
const userCache = new NodeCache({ stdTTL: 600 }) // 10分钟缓存
```

## 扩展功能建议

1. **二维码生成**: 集成qrcode库自动生成二维码
2. **图片处理**: 自动压缩和格式转换
3. **批量导入**: Excel批量导入二维码
4. **使用统计**: 二维码扫描次数统计
5. **定时任务**: 定期检查二维码有效性
6. **权限细化**: 不同用户类型的操作权限
7. **操作日志**: 详细的操作记录
8. **数据导出**: 支持Excel导出功能

## 维护说明

### 日志监控
- 关键操作添加日志记录
- 错误信息详细记录
- 性能指标监控

### 备份策略
- 定期备份二维码图片文件
- 数据库定期备份
- 配置文件版本控制

### 安全注意事项
- 文件上传安全检查
- SQL注入防护
- 权限验证完整性
- 输入参数验证
