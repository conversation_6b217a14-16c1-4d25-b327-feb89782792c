import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import './styles/app.scss'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'

import { AddDateFormatProto } from './utils/date'
import companyManager from '@/manager/companyManager'
import orderManager from '@/manager/orderManager'
import contextManager from '@/manager/contextManager'
import { useAuthStore } from '@/stores/auth'
import { useAppStore } from '@/stores/app'

//拓展时间对象原型format方法
AddDateFormatProto();

const app = createApp(App)
const pinia = createPinia()

// 注册Pinia
app.use(pinia)

// 注册Element Plus
app.use(ElementPlus)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 注册路由
app.use(router)

const tasks = [
  companyManager.fetchAllCities(),
  orderManager.queryCrops(),
  contextManager.getUserInfo()
]

// 确保系统基础数据拉取完成后才app才启动
Promise.all(tasks).then((resArr) => {
  const authStore = useAuthStore()
  const appStore = useAppStore()
  
  appStore.setAreas(resArr[0])
  appStore.setCrops(resArr[1])
  authStore.login(resArr[2])
}).catch((err) => {
  console.log('初始化数据加载失败:', err)
}).finally(() => {
  app.mount('#app')
})
