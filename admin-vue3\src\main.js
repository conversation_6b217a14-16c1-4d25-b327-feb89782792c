import Vue from 'vue'
import ElementUI from 'element-ui'
import './styles/app.scss'
import 'element-ui/lib/theme-chalk/index.css'
import App from './App.vue'
import router from './router'
import store from './store'

import { AddDateFormatProto } from './utils/date'
import companyManager from '@/manager/companyManager'
import orderManager from '@/manager/orderManager'
import contextManager from '@/manager/contextManager'

//拓展时间对象原型format方法
AddDateFormatProto();

Vue.use(ElementUI);
Vue.config.productionTip = false

const tasks = [
  companyManager.fetchAllCities(),
  orderManager.queryCrops(),
  contextManager.getUserInfo()
]

// 确保系统基础数据拉取完成后才app才启动
Promise.all(tasks).then((resArr) => {
  store.commit('SET_AREA', resArr[0])
  store.commit('SET_CROP', resArr[1])
  store.commit('LOGIN', resArr[2])
}).catch((err) => {
  console.log(err)
}).then(() => {
  new Vue({
    router,
    store,
    render: h => h(App)
  }).$mount('#app')
})
