export default{
    methods:{
        cityCodeToText(code){
            const areaArr = this.cityCodeCompletion(code);
            return areaArr.map(item => item.name).join('/');
        },
        cityCodeToSelectorValue(code){
            const areaArr = this.cityCodeCompletion(code);
            return areaArr.map(item => item.code);
        },
        cityCodeCompletion(code){
            let arr = [];
            if(this.$store.state.areaArr.length > 0){
                this.$store.state.areaArr.some(level1 => {
                    if(level1.code === code){
                        arr = [level1];
                        return true;
                    }else{
                        return level1.children.some(level2 => {
                            if(level2.code === code){
                                arr = [level1, level2];
                                return true
                            }else{
                                return level2.children.some(level3 => {
                                    if(level3.code === code){
                                        arr = [level1, level2, level3];
                                        return true
                                    }else{
                                        return false
                                    }
                                })
                            }
                        })
                    }
                })
            }
            return arr;
        }
    }
}