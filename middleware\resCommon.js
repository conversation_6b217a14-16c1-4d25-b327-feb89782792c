const { USER_TYPE } = require('../constant/enum')
function sendSuccess (data) {
    this.json({
        code: 1,
        data
    })
}
function sendMessage (err) {
    const message = err instanceof Error ? err.message : err
    this.json({
        code: 0,
        message
    })
}
module.exports = function (req, res, next) {
    const { userId, isAdmin, isCollector, isBuyer } = req.session;
    if (userId) {
        req.body.userId = userId;
        req.body.userSessionType = { isAdmin, isCollector, isBuyer };
    } else {
        req.body.userId = null;
        req.body.userSessionType = null;
    }
    res.sendSuccess = sendSuccess;
    res.sendMessage = sendMessage;
    next()
}