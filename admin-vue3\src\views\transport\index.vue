<template>
	<div class="card">
		<el-tabs v-model="currentTabType" @tab-click="handleTabChange">
			<el-tab-pane label="厂车" name="0"></el-tab-pane>
			<el-tab-pane label="锦州港" name="1"></el-tab-pane>
		</el-tabs>
		<div class="search_wrapper">
			<el-input
				v-model="queryParams.factoryName"
				placeholder="请输入厂名"
				clearable
				style="width: 200px"
			></el-input>

			<el-button type="primary" @click="loadData">查询</el-button>
			<el-button @click="clear">清空</el-button>
			<el-button style="width: 140px" type="primary" @click="openAddFactoryDialog"
				>新增工厂</el-button
			>
		</div>
		<div class="card-body">
			<el-table
				v-loading="loading"
				:data="tableData"
				row-key="id"
				border
				fit
				highlight-current-row
				style="width: 100%"
			>
				<el-table-column label="ID" prop="id" align="center" width="100px" />
				<el-table-column label="厂名" prop="factoryName" align="center" />
				<el-table-column label="当前数据" prop="todayData" align="center" width="200px">
					<template slot-scope="{ row }"
						>{{ row.todayData }}{{ row.isSystemData ? '' : '车' }}</template
					>
				</el-table-column>
				<el-table-column label="较前一日变化" prop="minusYesterday" align="center" width="200px">
					<template slot-scope="{ row }">
						{{ formatMinusYesterday(row.minusYesterday, row.isSystemData) }}
					</template>
				</el-table-column>
				<el-table-column label="记录日期" prop="date" align="center" width="180px">
					<template slot-scope="{ row }">{{ new Date(row.date).format('yyyy-MM-dd') }}</template>
				</el-table-column>
				<el-table-column label="操作" align="center" width="260">
					<template slot-scope="{ row }">
						<el-button type="primary" size="small" @click="onUpdateRow(row)">更新</el-button>
						<el-button type="success" size="small" @click="showHistory(row)">详情</el-button>
						<el-button v-if="!row.isSystemData" type="danger" size="small" @click="onDelete(row)"
							>删除</el-button
						>
					</template>
				</el-table-column>
				<template slot="empty">
					<el-empty description="暂无数据"></el-empty>
				</template>
			</el-table>
			<Pagination
				v-if="total > 0"
				:total="total"
				:pageIndex.sync="pageIndex"
				:pageSize.sync="pageSize"
				@pagination="loadData"
			/>
		</div>

		<el-dialog :title="dialogTitle" :visible.sync="showEditDialog" :close-on-click-modal="false">
			<el-form ref="addFactoryForm" :model="editRow" :rules="formRules" label-width="120px">
				<el-form-item label="厂名" prop="factoryName">
					<el-input
						v-model="editRow.factoryName"
						placeholder="请输入厂名"
						clearable
						:disabled="editRow.isSystemData"
						:maxlength="50"
						style="width: 90%"
					></el-input>
				</el-form-item>
				<el-form-item label="当前数据" prop="todayData">
					<el-input-number v-model="editRow.todayData" :min="0"></el-input-number>
					{{ editRow.isSystemData ? '' : '车' }}
				</el-form-item>
				<el-form-item label="记录日期" prop="date">
					<el-date-picker
						v-model="editRow.date"
						type="date"
						:default-value="new Date()"
						placeholder="选择日期"
					></el-date-picker>
				</el-form-item>
			</el-form>
			<span slot="footer" class="dialog-footer">
				<el-button @click="closeDialog">取 消</el-button>
				<el-button type="primary" @click="saveRowData">确 定</el-button>
			</span>
		</el-dialog>

		<!-- 历史记录对话框 -->
		<el-dialog title="历史变更记录" :visible.sync="showHistoryDialog" width="900px">
			<el-table :data="historyData" border v-loading="historyLoading">
				<el-table-column prop="newBusinessDate" label="当前数据">
					<template slot-scope="{ row }">
						<p>数值：{{ row.newTodayData }}{{ row.isSystemData ? '' : '车' }}</p>
						记录日期：{{ new Date(row.newBusinessDate).format('yyyy-MM-dd') }}
					</template>
				</el-table-column>
				<el-table-column prop="oldBusinessDate" label="前一日数据">
					<template slot-scope="{ row }">
						<p>数值：{{ row.oldTodayData }}{{ row.isSystemData ? '' : '车' }}</p>
						记录日期：{{
							row.oldBusinessDate ? new Date(row.oldBusinessDate).format('yyyy-MM-dd') : '初始化'
						}}
					</template>
				</el-table-column>
				<el-table-column prop="minusYesterday" align="center" label="较前一日变化">
					<template slot-scope="{ row }">
						{{ formatMinusYesterday(row.newTodayData - row.oldTodayData, row.isSystemData) }}
					</template>
				</el-table-column>
				<el-table-column prop="createTime" label="更新时间" align="censter" width="180">
					<template slot-scope="{ row }">{{
						new Date(row.createTime).format('yyyy-MM-dd hh:mm:ss')
					}}</template>
				</el-table-column>
				<template slot="empty">
					<el-empty description="暂无历史记录"></el-empty>
				</template>
			</el-table>
		</el-dialog>
	</div>
</template>

<script>
	import transportManager from '@/manager/transportManager'
	import Pagination from '@/components/Pagination'
	import tableMixin from '@/mixins/tableMixin'
	export default {
		name: 'Transport',
		components: { Pagination },
		mixins: [tableMixin],
		data() {
			return {
				currentTabType: '0',
				queryParams: {
					factoryName: '',
					type: '0'
				},
				tableData: [],
				total: 0,
				pageIndex: 0,
				pageSize: 10,
				loading: false,
				showEditDialog: false,
				editRow: {},
				dialogTitle: '',
				formRules: {
					factoryName: [
						{ required: true, message: '厂名不能为空', trigger: 'blur' },
						{ max: 50, message: '长度不能超过50字符', trigger: 'blur' }
					],
					todayData: [
						{ type: 'number', required: true, min: 0, message: '数值不能为空', trigger: 'blur' }
					],
					date: [{ type: 'date', required: true, message: '请选择时间', trigger: 'change' }]
				},
				showHistoryDialog: false,
				historyData: [],
				currentTransportId: null,
				historyLoading: false
			}
		},
		methods: {
			formatMinusYesterday(val, isSystemData = false) {
				if (isSystemData) {
					return val
				}
				if (val === 0) return '持平'
				return `${val > 0 ? '+' : ''}${val}车`
			},
			handleTabChange(tab) {
				this.queryParams.type = tab.name
				this.refreshData()
			},
			clear() {
				this.queryParams = { ...this.queryParams, factoryName: '' }
				this.refreshData()
			},
			async loadData() {
				try {
					this.loading = true
					const params = {
						...this.queryParams,
						pageIndex: this.pageIndex,
						pageSize: this.pageSize
					}
					const res = await transportManager.queryList(params)
					this.tableData = res.list || []
					this.total = res.total || 0
				} catch (error) {
					this.$message.error(error)
				} finally {
					this.loading = false
				}
			},
			refreshData() {
				this.pageIndex = 0
				this.loadData()
			},
			onDelete(row) {
				this.$confirm(`此操作将永久删除【${row.factoryName}】的记录, 是否继续?`, '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				})
					.then(() => {
						transportManager
							.deleteFactory(row.id)
							.then(() => {
								this.$message.success('删除成功!')
								this.loadData()
							})
							.catch(err => {
								this.$message.error(err)
							})
					})
					.catch(err => {
						this.$message.info(err === 'cancel' ? '已取消删除' : err.message)
					})
			},
			openAddFactoryDialog() {
				this.dialogTitle = '新增工厂信息'
				this.showEditDialog = true
				this.editRow = {
					date: new Date()
				}
			},
			onUpdateRow(row) {
				this.dialogTitle = '更新工厂信息'
				this.showEditDialog = true
				this.editRow = { ...row, date: new Date(row.date) }
			},
			saveRowData() {
				this.$refs.addFactoryForm.validate(valid => {
					if (valid) {
						const { id, factoryName, todayData, date } = this.editRow
						const payload = {
							factoryName,
							todayData,
							date: new Date(date).format('yyyy-MM-dd'),
							type: this.queryParams.type
						}
						const action = this.editRow.id
							? transportManager.updateFactory(this.editRow.id, payload)
							: transportManager.addFactory(payload)
						action
							.then(() => {
								this.$message.success(`${this.editRow.id ? '更新' : '新增'}成功!`)
								this.closeDialog()
								this.loadData()
							})
							.catch(err => {
								this.$message.error(err.message)
							})
					} else {
						console.log('error submit!!')
						return false
					}
				})
			},

			closeDialog() {
				this.showEditDialog = false
				this.editRow = {}
			},
			async showHistory(row) {
				try {
					this.showHistoryDialog = true
					this.historyLoading = true
					this.currentTransportId = row.id
					const res = await transportManager.getHistoryRecords(row.id)
					this.historyData = res.list || []
				} catch (error) {
					this.$message.error('获取历史记录失败: ' + error.message)
				} finally {
					this.historyLoading = false
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.card {
		padding: 20px;
		background-color: #fff;
		border-radius: 4px;
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	}

	.search_wrapper {
		margin-bottom: 20px;
	}

	.el-table {
		margin-bottom: 20px;
	}

	.el-table th {
		background-color: #f5f7fa;
	}

	.el-table .cell {
		word-break: break-all;
	}

	.el-table .el-button {
		margin-right: 5px;
	}
	.history-table {
		margin: -20px;

		.el-table__body {
			font-size: 14px;
		}

		.el-table__empty-block {
			min-height: 100px;
		}
	}
</style>
