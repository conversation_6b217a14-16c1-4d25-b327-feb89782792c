<template>
    <div id="app">
        <div class="home_root" :class="{ sidebar_collapse: appStore.isMenuCollapsed }" v-if="authStore.isAuthenticated">
            <Sidebar />
            <Navbar />
            <router-view />
        </div>
        <router-view v-else />
    </div>
</template>

<script setup>
import { useAuthStore } from '@/stores/auth'
import { useAppStore } from '@/stores/app'
import Sidebar from './views/Sidebar.vue'
import Navbar from './views/Navbar.vue'

const authStore = useAuthStore()
const appStore = useAppStore()
</script>
<style lang="scss">
.home_root {
    margin-left: 180px;
    padding: 66px 10px 15px;
    min-height: 100vh;
    box-sizing: border-box;
    &.sidebar_collapse {
        margin-left: 64px;
    }
}
</style>