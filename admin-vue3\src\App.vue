<template>
    <div id="app">
        <div class="home_root" :class="{ sidebar_collapse: $store.state.collapseMenu }" v-if="$store.state.logged">
            <Sidebar />
            <Navbar />
            <router-view />
        </div>
        <Login v-else />
    </div>
</template>

<script>

import Login from './views/Login'
import Sidebar from './views/Sidebar'
import Navbar from './views/Navbar'
export default {
    components: { Login, Sidebar, Navbar },
    created () {
        if (!this.$store.state.logged && this.$route.path !== '/login') {
            this.$router.push('/login')
        }
    }
}
</script>
<style lang="scss">
.home_root {
    margin-left: 180px;
    padding: 66px 10px 15px;
    min-height: 100vh;
    box-sizing: border-box;
    &.sidebar_collapse {
        margin-left: 64px;
    }
}
</style>