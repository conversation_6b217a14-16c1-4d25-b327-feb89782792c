import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

export default new Vuex.Store({
	state: {
		admin: null,
		collapseMenu: true,
		logged: false,
		cropArr: [],
		cropMap: {},
		areaArr: []
	},
	getters: {

	},
	mutations: {
		LOGOUT: (state) => {
			state.logged = false;
		},
		LOGIN: (state, admin) => {
			state.admin = admin;
			state.logged = true;
		},
		SIDEBAR_COLLAPSE_CHANGE: (state) => {
			state.collapseMenu = !state.collapseMenu;
		},
		SET_CROP: (state, crops) => {
			let cropMap = {};
			crops.forEach(item => {
				cropMap[item.id] = item;
			});
			state.cropArr = crops;
			state.cropMap = cropMap;
		},
		SET_AREA: (state, arr) => {
			state.areaArr = arr;
		}
	},
	actions: {

	},
	modules: {

	}
})
