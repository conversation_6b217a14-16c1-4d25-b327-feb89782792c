{"name": "admin-vue3", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.3.0", "vue-router": "^4.1.0", "pinia": "^2.0.0", "element-plus": "^2.3.0", "@element-plus/icons-vue": "^2.1.0", "axios": "^1.4.0", "md5": "^2.3.0", "core-js": "^3.30.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.0", "vite": "^4.3.0", "sass": "^1.62.0", "eslint": "^8.40.0", "eslint-plugin-vue": "^9.10.0", "@vue/eslint-config-prettier": "^7.1.0", "prettier": "^2.8.0"}}