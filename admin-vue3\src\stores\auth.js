import { defineStore } from 'pinia'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    admin: {
      username: '',
      avatar: ''
    },
    logged: false
  }),
  
  getters: {
    isLoggedIn: (state) => state.logged,
    isAuthenticated: (state) => state.logged,
    currentAdmin: (state) => state.admin
  },
  
  actions: {
    login(admin) {
      this.admin = admin
      this.logged = true
    },
    
    logout() {
      this.admin = {
        username: '',
        avatar: ''
      }
      this.logged = false
    }
  }
})