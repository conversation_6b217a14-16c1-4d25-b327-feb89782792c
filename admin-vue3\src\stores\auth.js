import { defineStore } from 'pinia'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    admin: null,
    logged: false
  }),
  
  getters: {
    isLoggedIn: (state) => state.logged,
    currentAdmin: (state) => state.admin
  },
  
  actions: {
    login(admin) {
      this.admin = admin
      this.logged = true
    },
    
    logout() {
      this.admin = null
      this.logged = false
    }
  }
})