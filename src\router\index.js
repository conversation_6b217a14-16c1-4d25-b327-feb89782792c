import Vue from 'vue'
import VueRouter from 'vue-router'

import OrderList from '@/views/order/List.vue'
import CropCompany from '@/views/price/Company.vue'
import CompanyDetail from '@/views/price/CompanyDetail.vue'
import CropPrice from '@/views/price/CropPrice.vue'
import CropOther from '@/views/price/CropOther.vue'
import Transport from '@/views/transport/index.vue'
import UserList from '@/views/user/List'
import InfoList from '@/views/info/List'
import OpenList from '@/views/home/<USER>'
import CropList from '@/views/system/CropList.vue'
import RegionList from '@/views/system/RegionList.vue'
import SystemConfig from '@/views/system/SystemConfig.vue'
import QrcodeList from '@/views/qrcode/List.vue'

Vue.use(VueRouter)

const cropSubmenu = {
	title: '系统设置',
	icon: 'el-icon-setting'
}

const priceSubmenu = {
	title: '价格管理',
	iconText: '￥'
}

// const userSubmenu = {
// 	title: '用户管理',
// 	icon: 'el-icon-user-solid'
// }

const routes = [
	{
		path: '/',
		name: 'SysConfig',
		component: SystemConfig,
		meta: {
			title: '小程启动配置',
			submenu: cropSubmenu
		}
	},
	{
		path: '/crop/list',
		name: 'CropList',
		component: CropList,
		meta: {
			title: '农作物管理',
			submenu: cropSubmenu
		}
	},
	{
		path: '/region/list',
		name: 'RegionList',
		component: RegionList,
		meta: {
			title: '价格页区域管理',
			submenu: cropSubmenu
		}
	},
	{
		path: '/open/log',
		name: 'OpenManager',
		component: OpenList,
		meta: {
			title: '应用启动统计',
			icon: 'el-icon-switch-button'
		}
	},
	{
		path: '/order',
		name: 'OrderManager',
		component: OrderList,
		meta: {
			title: '订单列表',
			icon: 'el-icon-menu'
		}
	},
	{
		path: '/price/company',
		name: 'CropCompany',
		component: CropCompany,
		meta: {
			title: '收购厂商',
			submenu: priceSubmenu
		}
	},
	{
		path: '/price/companyDetail',
		name: 'CropCompanyDetail',
		component: CompanyDetail,
		meta: {
			title: '收购标准',
			submenu: priceSubmenu
		}
	},
	{
		path: '/price/list',
		name: 'CropPrice',
		component: CropPrice,
		meta: {
			title: '收购价格',
			submenu: priceSubmenu
		}
	},
	{
		path: '/price/port',
		name: 'CropOther',
		component: CropOther,
		meta: {
			title: '港口信息',
			submenu: priceSubmenu
		}
	},
	{
		path: '/transport/index',
		name: 'Transport',
		component: Transport,
		meta: {
			title: '运输信息',
			submenu: priceSubmenu
		}
	},
	{
		path: '/info/list',
		name: 'InfoList',
		component: InfoList,
		meta: {
			title: '资讯管理',
			icon: 'el-icon-news'
		}
	},
	{
		path: '/user',
		name: 'QwcUser',
		component: UserList,
		meta: {
			title: '用户管理',
			icon: 'el-icon-user-solid'
		}
	},
	{
		path: '/qrcode',
		name: 'QrcodeManager',
		component: QrcodeList,
		meta: {
			title: '微信群二维码管理',
			icon: 'el-icon-picture'
		}
	},
	{
		path: '*',
		redirect: '/'
	}
]

const router = new VueRouter({
	routes
})

export default router
