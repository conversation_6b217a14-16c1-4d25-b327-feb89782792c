# Project Structure

## Root Directory
- `.browserslistrc` - Browser compatibility configuration
- `.eslintrc.js` - ESLint configuration
- `.gitignore` - Git ignore rules
- `.nvmrc` - Node version specification
- `babel.config.js` - Babel configuration
- `deploy.sh` - Deployment script
- `jsconfig.json` - JavaScript configuration for editor support
- `package.json` - Project dependencies and scripts
- `vue.config.js` - Vue CLI configuration

## Source Code Organization

### Main Directories
- `src/` - Application source code
- `public/` - Static assets that will be served as-is
- `dist/` - Build output (generated)
- `node_modules/` - Dependencies (generated)

### Source Code Structure (`src/`)
- `components/` - Reusable Vue components
- `images/` - Image assets
- `layout/` - Layout components
- `manager/` - Data managers for API interactions
  - `companyManager.js` - Company data operations
  - `orderManager.js` - Order data operations
  - `contextManager.js` - User context and session management
- `mixins/` - Vue mixins for shared component logic
- `router/` - Vue Router configuration
- `store/` - Vuex store modules
- `styles/` - SCSS stylesheets
  - `app.scss` - Main stylesheet
- `utils/` - Utility functions
  - `date.js` - Date formatting utilities
- `views/` - Page components
  - `Login.vue` - Login page
  - `Sidebar.vue` - Sidebar navigation
  - `Navbar.vue` - Top navigation bar
- `App.vue` - Root Vue component
- `main.js` - Application entry point

## Architecture Patterns

### Application Initialization
The application initializes by loading essential data (cities, crops, user info) before mounting the Vue instance.

### Authentication Flow
- Authentication state is stored in Vuex
- Unauthenticated users are redirected to the login page
- Authenticated users see the main layout with sidebar and navbar

### Data Management
- API interactions are abstracted through manager modules
- Data is stored in Vuex store
- Components access data through Vuex getters

### UI Components
- Uses Element UI as the primary component library
- Custom components extend or complement Element UI
- SCSS is used for styling with component-scoped styles