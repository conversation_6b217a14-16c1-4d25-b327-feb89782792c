# 微信群二维码管理系统 - 数据库设计

## 数据表设计

### 1. 微信群二维码表 (wechat_group_qrcodes)

```sql
CREATE TABLE `wechat_group_qrcodes` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) DEFAULT NULL COMMENT '关联用户ID，NULL表示系统级二维码',
  `qr_code_url` varchar(500) NOT NULL COMMENT '二维码图片URL',
  `description` text COMMENT '描述信息',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否生效 (1:生效, 0:不生效)',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator_id` int(11) DEFAULT NULL COMMENT '创建者ID（管理员ID）',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_qrcode_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信群二维码表';
```

### 2. 用户表 (users) - 已存在

用户表已经存在，包含以下主要字段：
- `id` - 用户ID (主键)
- `name` - 用户名/企业名称
- `mobile` - 手机号
- `type` - 用户类型 (0:普通用户, 1:收购商, 2:粮庄, 3:设备商, 10:信息收集员, 100:管理员)
- `address` - 地址
- `avatar` - 头像
- `create_time` - 创建时间
- 其他业务字段...

## 数据关系说明

1. **用户与二维码关系**：一对多关系
   - 一个用户可以有多个二维码
   - 一个二维码只能属于一个用户（或系统级）

2. **系统级二维码**：
   - `user_id` 为 NULL 的记录表示系统级二维码
   - 系统级二维码不归属于任何用户
   - 可以独立管理和维护

3. **权限控制**：
   - 只有管理员可以创建、编辑、删除二维码
   - 记录创建者信息便于审计

## API 接口设计

### 1. 查询二维码列表
- **接口**: `POST /api/v1/admin/qrcode/load`
- **参数**: 
  - `userName` - 用户名（可选）
  - `userMobile` - 用户手机号（可选）
  - `isActive` - 是否生效（可选）
  - `pageIndex` - 页码
  - `pageSize` - 每页数量

### 2. 创建二维码
- **接口**: `POST /api/v1/admin/qrcode/create`
- **参数**:
  - `userId` - 用户ID（可选，为空表示系统级）
  - `qrCodeUrl` - 二维码图片URL
  - `description` - 描述信息
  - `isActive` - 是否生效

### 3. 更新二维码
- **接口**: `PUT /api/v1/admin/qrcode/update/{id}`
- **参数**:
  - `qrCodeUrl` - 二维码图片URL（可选）
  - `description` - 描述信息（可选）
  - `isActive` - 是否生效（可选）

### 4. 删除二维码
- **接口**: `DELETE /api/v1/admin/qrcode/delete/{id}`

### 5. 获取二维码详情
- **接口**: `GET /api/v1/admin/qrcode/detail/{id}`

### 6. 上传二维码图片
- **接口**: `POST /api/v1/admin/qrcode/upload`
- **参数**: `file` - 图片文件

### 7. 获取用户列表
- **接口**: `GET /api/v1/admin/qrcode/users`
- **说明**: 返回所有用户列表，用于下拉选择

### 8. 批量更新状态
- **接口**: `POST /api/v1/admin/qrcode/batch-status`
- **参数**:
  - `ids` - 二维码ID数组
  - `isActive` - 是否生效

## 功能特性

1. **完整的CRUD操作**：支持二维码的增删改查
2. **用户关联**：可以将二维码关联到具体用户
3. **系统级二维码**：支持不归属任何用户的系统级二维码
4. **状态管理**：支持启用/禁用二维码
5. **图片上传**：支持二维码图片的上传和管理
6. **搜索过滤**：支持按用户、状态等条件搜索
7. **分页显示**：支持大量数据的分页展示
8. **权限控制**：只有管理员可以操作

## 前端实现

- **技术栈**: Vue 2 + Element UI + Vuex + Vue Router
- **主要组件**: 
  - `src/views/qrcode/List.vue` - 主页面
  - `src/manager/qrcodeManager.js` - API管理器
- **路由**: `/qrcode` - 微信群二维码管理
- **功能**: 表格展示、搜索、新增、编辑、删除、状态切换
