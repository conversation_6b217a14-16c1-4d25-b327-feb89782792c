<template>
    <div class="sidebar_root" :class="{ sidebar_collapse: appStore.isMenuCollapsed }">
        <div class="sidebar_wrapper">
            <el-menu
                :collapse="appStore.isMenuCollapsed"
                :router="true"
                :default-active="defaultActive"
                background-color="#545c64"
                text-color="#ffffff"
                active-text-color="#ffd04b">
                <template v-for="(menu, index1) in menus" :key="index1">
                    <el-sub-menu :index="`${index1 + 1}`" v-if="menu.routes">
                        <template #title>
                            <el-icon v-if="menu.iconComponent">
                                <component :is="menu.iconComponent" />
                            </el-icon>
                            <i v-else-if="menu.icon" :class="menu.icon"></i>
                            <span v-else>{{ menu.iconText }}</span>
                            <span>{{ menu.title }}</span>
                        </template>
                        <el-menu-item 
                            v-for="(route, index2) in menu.routes" 
                            :key="index2" 
                            :index="`${index1 + 1}-${index2 + 1}`" 
                            :route="route.path">
                            {{ route.meta.title }}
                        </el-menu-item>
                    </el-sub-menu>
                    <el-menu-item :index="`${index1 + 1}`" :route="menu.path" v-else>
                        <el-icon v-if="menu.meta.iconComponent">
                            <component :is="menu.meta.iconComponent" />
                        </el-icon>
                        <i v-else-if="menu.meta.icon" :class="menu.meta.icon"></i>
                        <span v-else>{{ menu.meta.iconText }}</span>
                        <template #title>{{ menu.meta.title }}</template>
                    </el-menu-item>
                </template>
            </el-menu>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAppStore } from '@/stores/app'
import { 
    Setting, 
    SwitchButton, 
    Menu, 
    News, 
    UserFilled, 
    Picture 
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const appStore = useAppStore()

const defaultActive = ref('1')
const menus = ref([])

// 图标映射
const iconMap = {
    'el-icon-setting': Setting,
    'el-icon-switch-button': SwitchButton,
    'el-icon-menu': Menu,
    'el-icon-news': News,
    'el-icon-user-solid': UserFilled,
    'el-icon-picture': Picture
}

onMounted(() => {
    const menuList = []
    let activeIndex = '1'
    const submenuIndexMap = {}
    
    router.getRoutes().forEach(route => {
        if (route.path !== '*' && route.path !== '/login') {
            if (route.meta?.submenu) {
                const submenuTitle = route.meta.submenu.title
                if (submenuIndexMap[submenuTitle] !== undefined) {
                    menuList[submenuIndexMap[submenuTitle]].routes.push(route)
                } else {
                    submenuIndexMap[submenuTitle] = menuList.length
                    const submenu = { ...route.meta.submenu }
                    if (submenu.icon && iconMap[submenu.icon]) {
                        submenu.iconComponent = iconMap[submenu.icon]
                    }
                    menuList.push({
                        ...submenu,
                        routes: [route]
                    })
                }
            } else {
                const menuItem = { ...route }
                if (menuItem.meta?.icon && iconMap[menuItem.meta.icon]) {
                    menuItem.meta.iconComponent = iconMap[menuItem.meta.icon]
                }
                menuList.push(menuItem)
            }
        }
    })
    
    // 找到当前激活的菜单项
    menuList.some((menu, index1) => {
        if (menu.name === route.name) {
            activeIndex = `${index1 + 1}`
            return true
        } else if (menu.routes) {
            return menu.routes.some((routeItem, index2) => {
                if (routeItem.name === route.name) {
                    activeIndex = `${index1 + 1}-${index2 + 1}`
                    return true
                }
            })
        }
    })
    
    menus.value = menuList
    defaultActive.value = activeIndex
})
</script>

<style lang="scss">
.sidebar_root{
    width: 180px;
    height: 100%;
    position: fixed;
    left: 0;
    top: 0;
    background: #545c64;
    z-index: 100;
    &.sidebar_collapse{
        width: 64px;
    }
    .sidebar_wrapper{
        height: 100%;
        overflow-x: hidden;
        overflow-y: auto;
    }
    .el-menu{
        border-right: 0;
    }
}
</style>