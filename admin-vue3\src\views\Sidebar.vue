<template>
    <div class="sidebar_root" :class="{ sidebar_collapse: $store.state.collapseMenu }">
        <div class="sidebar_wrapper">
            <el-menu
                :collapse="$store.state.collapseMenu"
                :router="true"
                :default-active="defaultActive"
                background-color="#545c64"
                text-color="#ffffff"
                active-text-color="#ffd04b">
                <template v-for="(menu, index1) in menus">
                    <el-submenu :index="`${index1 + 1}`" :key="index1" v-if="menu.routes">
                        <template slot="title">
                            <i :class="menu.icon" v-if="menu.icon"></i>
                            <i class="el-icon-" v-else>{{menu.iconText}}</i>
                            <span slot="title">{{menu.title}}</span>
                        </template>
                        <el-menu-item v-for="(route, index2) in menu.routes" :key="index2" :index="`${index1 + 1}-${index2 + 1}`" :route="route">{{route.meta.title}}</el-menu-item>
                    </el-submenu>
                    <el-menu-item :index="`${index1 + 1}`" :key="index1" :route="menu" v-else>
                        <i :class="menu.meta.icon" v-if="menu.meta.icon"></i>
                        <i v-else>{{menu.meta.iconText}}</i>
                        <span slot="title">{{menu.meta.title}}</span>
                    </el-menu-item>
                </template>
            </el-menu>
        </div>
    </div>
</template>

<script>

export default {
    data(){
        return {
            defaultActive: '1',
            menus: []
        }
    },
    created(){
        let menus = [];
        let defaultActive;
        let submenuIndexMap = {};
        this.$router.options.routes.forEach(route => {
            if(route.path != '*'){
                if(route.meta.submenu){
                    if(submenuIndexMap[route.meta.submenu.title] !== undefined){
                        menus[submenuIndexMap[route.meta.submenu.title]].routes.push(route)
                    }else{
                        submenuIndexMap[route.meta.submenu.title] = menus.length;
                        menus.push(Object.assign({}, route.meta.submenu, {
                            routes: [route]
                        }))
                    }
                }else{
                    menus.push(route);
                }
            }
        })
        menus.some((menu, index1) => {
            if(menu.name === this.$route.name){
                defaultActive = `${index1 + 1}`;
                return true;
            }else if(menu.routes){
                return menu.routes.some((route, index2) => {
                    if(route.name === this.$route.name){
                        defaultActive = `${index1 + 1}-${index2 + 1}`;
                        return true;
                    }
                })
            }
        })
        this.menus = menus;
        this.defaultActive = defaultActive;
    }
}
</script>

<style lang="scss">
.sidebar_root{
    width: 180px;
    height: 100%;
    position: fixed;
    left: 0;
    top: 0;
    background: #545c64;
    z-index: 100;
    &.sidebar_collapse{
        width: 64px;
    }
    .sidebar_wrapper{
        height: 100%;
        overflow-x: hidden;
        overflow-y: auto;
    }
    .el-menu{
        border-right: 0;
    }
}
</style>