const axios = require('axios')
const fs = require('fs')
const path = require('path')
const sharp = require('sharp')

class OCRClient {
	constructor(apiKey, secretKey) {
		this.apiKey = apiKey
		this.secretKey = secretKey
		this.accessToken = null
		this.tokenExpiresAt = 0
	}

	getErrorMessage(errorCode) {
		const map = {
			6: '无权限访问数据，请检查 API Key 和 Secret Key 是否正确，或是否开启了相关 OCR 权限',
			17: '每天请求次数超限，请检查百度控制台额度',
			18: 'QPS 超限（短时间内请求过多），请降低频率',
			100: '无效的 access_token，请重新获取',
			216201: '图片格式错误（可能不是 base64 编码）',
			216202: '图片太大，超过 4MB 或 base64 长度超过限制',
			216630: '识别失败，可能为模糊图片或干扰太强'
			// ...其他错误码可继续扩充
		}
		return map[errorCode] || '未知错误'
	}

	async getAccessToken() {
		const now = Date.now()
		if (this.accessToken && now < this.tokenExpiresAt) {
			return this.accessToken
		}

		const response = await axios.get('https://aip.baidubce.com/oauth/2.0/token', {
			params: {
				grant_type: 'client_credentials',
				client_id: this.apiKey,
				client_secret: this.secretKey
			}
		})

		const data = response.data
		this.accessToken = data.access_token
		this.tokenExpiresAt = now + data.expires_in * 1000 - 60 * 1000
		return this.accessToken
	}

	async preprocessImage(imagePath) {
		const processedBuffer = await sharp(imagePath)
			.grayscale()
			.normalize() // 增强对比度
			.sharpen() // 提高清晰度
			.toFormat('png')
			.toBuffer()
		return processedBuffer.toString('base64')
	}

	async imageToBase64(imagePath, preprocess = true) {
		if (!fs.existsSync(imagePath)) {
			throw new Error(`文件不存在: ${imagePath}`)
		}
		if (preprocess) {
			return await this.preprocessImage(imagePath)
		} else {
			const image = fs.readFileSync(path.resolve(imagePath))
			return Buffer.from(image).toString('base64')
		}
	}

	async callOCRApi(apiEndpoint, imagePathOrBase64, options) {
		const accessToken = await this.getAccessToken()
		const image = fs.existsSync(imagePathOrBase64)
			? await this.imageToBase64(imagePathOrBase64, true)
			: imagePathOrBase64

		const url = `https://aip.baidubce.com/rest/2.0/ocr/v1/${apiEndpoint}?access_token=${accessToken}`
		try {
			const data = new URLSearchParams({ image, ...options })
			const response = await axios.post(url, data.toString(), {
				headers: {
					'Content-Type': 'application/x-www-form-urlencoded',
					Accept: 'application/json'
				},
				timeout: 10000
			})
			if (response.data.error_code) {
				const code = response.data.error_code
				const message = this.getErrorMessage(code)
				console.warn(`❗ OCR 失败：${code} - ${message}`)
				throw `[OCR ${code}] ${message}`
			}
			return response.data
		} catch (error) {
			console.error('❌ OCR 请求异常:', error.message || error)
			throw error
		}
	}

	async recognizeBasic(imagePathOrBase64) {
		try {
			const result = await this.callOCRApi('accurate_basic', imagePathOrBase64)
			return result
		} catch (err) {
			console.warn('⚠️ 高精度失败，尝试降级到 general_basic...')
			const result = await this.callOCRApi('general_basic', imagePathOrBase64)
			return result
		}
	}

	async recognizeTable(imagePathOrBase64) {
		const result = await this.callOCRApi('table', imagePathOrBase64, { return_excel: true })
		return this.parseTable(result)
	}

	parseTable(ocrResult) {
		const tables = ocrResult.tables_result || []
		if (!tables.length) return []

		const table = tables[0]
		const rows = []

		const maxRow = Math.max(...table.body.map(cell => cell.row_end)) + 1
		const maxCol = Math.max(...table.body.map(cell => cell.col_end)) + 1

		for (let i = 0; i < maxRow; i++) {
			rows[i] = new Array(maxCol).fill('')
		}

		for (const cell of table.body) {
			const { row_start, row_end, col_start, col_end, words } = cell
			for (let i = row_start; i <= row_end; i++) {
				for (let j = col_start; j <= col_end; j++) {
					if (i === row_start && j === col_start) {
						rows[i][j] = words
					}
				}
			}
		}

		return rows
	}
}

module.exports = OCRClient
