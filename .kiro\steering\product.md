# Product Overview

This is an admin dashboard application built with Vue.js. The application appears to be an administrative interface for managing agricultural data, including:

- Company management
- Order management
- Crop information
- City/area data

The application features a login system with authenticated routes and a standard admin layout with sidebar navigation and top navbar.

## Key Features

- User authentication and session management
- Company and order management interfaces
- Crop and geographical data management
- Responsive layout with collapsible sidebar

## Business Domain

The application appears to be focused on agricultural management, possibly for tracking orders, companies, and crop information across different geographical regions.