<template>
	<div class="card">
		<div class="search_wrapper">
			<el-input
				v-model="searchParams.mobile"
				placeholder="请输入手机"
				clearable
				:maxlength="11"
				style="width: 200px"
			></el-input>
			<el-input
				v-model="searchParams.name"
				placeholder="请输入名字"
				clearable
				style="width: 200px"
			></el-input>
			<el-input
				v-model="searchParams.address"
				placeholder="请输入地址"
				clearable
				style="width: 200px"
			></el-input>
			<el-select v-model="searchParams.type" placeholder="用户类型" clearable style="width: 120px">
				<el-option :value="0" label="普通用户"></el-option>
				<el-option :value="1" label="收购商"></el-option>
				<el-option :value="2" label="粮庄"></el-option>
				<el-option :value="3" label="设备商"></el-option>
				<el-option :value="10" label="信息收集员"></el-option>
				<el-option :value="100" label="管理员"></el-option>
			</el-select>
			<el-button type="primary" @click="loadData">查询</el-button>
			<el-button @click="clear">清空</el-button>
		</div>
		<div class="card-body">
			<el-table
				v-loading="loading"
				:data="tableData"
				border
				fit
				highlight-current-row
				style="width: 100%"
			>
				<el-table-column label="ID" prop="id" align="center" width="50"></el-table-column>
				<el-table-column label="头像" prop="avatar" align="center" width="100">
					<template slot-scope="{ row }">
						<el-avatar :size="60" :src="row.avatar" @error="errorHandler">
							<img src="https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png" />
						</el-avatar>
					</template>
				</el-table-column>
				<el-table-column label="姓名/企业名称" prop="name" align="center"></el-table-column>
				<el-table-column label="手机" prop="mobile" align="center" width="120"></el-table-column>
				<el-table-column label="用户类型" prop="type" align="center" width="100">
					<template slot-scope="{ row }">
						<div v-if="row.type === 100">管理员</div>
						<div v-else-if="row.type === 10">信息收集员</div>
						<div v-else-if="row.type === 1">收购商</div>
						<div v-else-if="row.type === 2">粮庄</div>
						<div v-else-if="row.type === 3">设备商</div>
						<div v-else>普通用户</div>
					</template>
				</el-table-column>
				<el-table-column label="设备商展示" prop="isPaid" align="center" width="100">
					<template slot-scope="{ row }">
						{{ row.type == 3 ? (row.isPaid ? '已上架' : '未上架') : '' }}
					</template>
				</el-table-column>
				<el-table-column label="地址" prop="address" align="center"></el-table-column>

				<el-table-column label="创建时间" prop="createTime" align="center" width="95">
					<template slot-scope="{ row }">{{
						new Date(row.createTime).format('yyyy-MM-dd hh:mm:ss')
					}}</template>
				</el-table-column>
				<el-table-column label="操作" align="center" width="150">
					<template slot-scope="{ row }">
						<span class="el_btn_box">
							<el-button
								v-if="row.type === 3"
								:type="row.isPaid ? 'danger' : 'success'"
								size="small"
								@click="onUpdateUserPaidState(row)"
								>{{ row.isPaid ? '下架' : '上架' }}</el-button
							>
							<el-button type="success" size="small" @click="onUserEdit(row)">编辑</el-button>
						</span>
					</template>
				</el-table-column>
			</el-table>
			<Pagination
				v-if="total > 0"
				:total="total"
				:pageIndex.sync="pageIndex"
				:pageSize.sync="pageSize"
				@pagination="loadData"
			/>
		</div>
		<el-dialog title="更新用户信息" :visible.sync="userDialogVisible">
			<el-form label-width="110px">
				<el-form-item label="用户ID">
					<el-input v-model="editUserItem.id" readonly></el-input>
				</el-form-item>
				<el-form-item label="用户名">
					<el-input v-model="editUserItem.name"></el-input>
				</el-form-item>
				<el-form-item label="手机号">
					<el-input v-model="editUserItem.mobile"></el-input>
				</el-form-item>
				<el-form-item label="地址">
					<el-input v-model="editUserItem.address"></el-input>
				</el-form-item>
				<el-form-item label="用户类型">
					<el-select v-model="editUserItem.type" placeholder="选择用户类型" clearable>
						<el-option :value="0" label="普通用户"></el-option>
						<el-option :value="1" label="收购商"></el-option>
						<el-option :value="2" label="粮庄"></el-option>
						<el-option :value="3" label="设备商"></el-option>
						<el-option :value="10" label="信息收集员"></el-option>
						<el-option :value="100" label="管理员"></el-option>
					</el-select>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button @click="userDialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="onEditUserBtnConfirm">确 定</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
	import userManager from '@/manager/userManager'
	import Pagination from '@/components/Pagination'
	import tableMixin from '@/mixins/tableMixin'
	export default {
		name: 'BuyerList',
		components: { Pagination },
		mixins: [tableMixin],
		data() {
			return {
				searchParams: {
					mobile: '',
					name: '',
					address: '',
					type: null
				},
				userDialogVisible: false,
				editUserItem: {}
			}
		},
		methods: {
			clear() {
				this.searchParams = {
					mobile: '',
					name: '',
					address: '',
					type: null
				}
			},
			loadData() {
				const {
					searchParams: { mobile, name, address, type },
					pageIndex,
					pageSize
				} = this
				this.loading = true
				userManager
					.queryUsers(mobile, name, address, type, pageIndex, pageSize)
					.then(res => {
						const { total, list } = res
						this.total = total
						this.tableData = list
					})
					.catch(err => {
						this.$message.error(err)
					})
					.then(() => {
						this.loading = false
					})
			},
			onUserEdit(item) {
				this.editUserItem = Object.assign({}, item)
				this.userDialogVisible = true
			},
			onEditUserBtnConfirm() {
				const { id, mobile, name, address, type } = this.editUserItem
				userManager
					.updateUser(id, { mobile, name, address, type })
					.then(() => {
						this.$message.success('更新成功')
						this.userDialogVisible = false
						this.loadData()
					})
					.catch(err => {
						this.$message.error(err)
					})
			},
			onUpdateUserPaidState(item) {
				const isPaid = item.isPaid ? 0 : 1
				userManager
					.updateUser(item.id, { isPaid })
					.then(() => {
						this.$message.success(`设备商${isPaid ? '上架' : '下架'}成功`)
						this.loadData()
					})
					.catch(err => {
						this.$message.error(err)
					})
			}
		}
	}
</script>
