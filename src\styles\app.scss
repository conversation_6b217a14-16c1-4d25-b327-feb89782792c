blockquote,body,button,dd,dl,dt,fieldset,form,h1,h2,h3,h4,h5,h6,hr,input,legend,li,ol,p,pre,td,textarea,th,ul,a,div,span{
    margin:0;
    padding:0;
    background-repeat:no-repeat;
}
body{
    font:10px/1.5 "Helvetica Neue",Helvetica,Arial,"Hiragino Sans GB",sans-serif;
    overflow:auto;
    -webkit-overflow-scrolling: touch;
    background:#ffffff;
    color: #333333;
}
h1,h2,h3,h4,h5,h6,button,input,select,textarea{
    font-size: 100%;
}
ol,ul{
    list-style:none;
}
img{border:0}
table{
    border-collapse:collapse;
    border-spacing:0;
}
.cf:after{
    display:block;
    clear:both;
    content:"";
    visibility:hidden;
    height:0;
}
.cf{zoom:1}
.fl{float:left}
.fr{float:right}
.search_wrapper{
    .el-cascader, .el-select, .el-input, .el-date-editor{
        margin-right: 10px;
        display: inline-block;
    }
    .el-button{
        width: 80px;
        margin-left: 20px;
    }
    .el-input{
        display: inline-block;
        max-width: 180px;
    }
    .add_btn{
        width: auto;
        float: right;
    }
}

.card-body{
    margin-top: 12px;
}

.el-table thead {
    color: #333333!important;
}

.modal{
    z-index: 9999;
}

.dialog_label{
    font-size: 16px;
    line-height: 2;
    margin-top: 15px;
}

.el_btn_box{
    margin: 5px 0;
    display: inline-block;
}

.el-cascader-menu__wrap{
    height: 204px!important;
}
.el-cascader-node{
    label{
        margin-bottom: 0;
    }
}