<template>
	<div class="card">
		<div class="search_wrapper">
			<el-input
				v-model="searchParams.title"
				placeholder="请输入标题"
				clearable
				style="width: 200px"
			></el-input>
			<el-select
				v-model="searchParams.cropId"
				clearable
				placeholder="请选择作物"
				@change="loadData"
			>
				<el-option v-for="crop in cropMaps" :key="crop.id" :label="crop.name" :value="crop.id" />
			</el-select>
			<el-checkbox-group v-model="searchParams.source" @change="handleChangeSource">
				<el-checkbox-button v-for="(value, key) in sourceLabels" :key="key" :label="key">{{
					value
				}}</el-checkbox-button>
			</el-checkbox-group>
			<el-button type="primary" @click="loadData">查询</el-button>
			<el-button @click="clear">清空</el-button>
			<div style="margin-left: auto">
				<el-button
					type="primary"
					icon="el-icon-refresh"
					style="width: auto"
					@click="scrapeDataDialogVisible = true"
					>更新数据</el-button
				>
			</div>
		</div>
		<div class="card-body">
			<el-table
				v-loading="loading"
				:data="tableData"
				border
				fit
				highlight-current-row
				style="width: 100%"
			>
				<el-table-column label="ID" prop="id" align="center" width="60"></el-table-column>
				<el-table-column label="标题" prop="title" align="center">
					<template slot-scope="{ row }">
						<el-link type="primary" :underline="false" :href="row.originalLink" target="_blank">{{
							row.title
						}}</el-link>
					</template>
				</el-table-column>
				<el-table-column label="来源" prop="source" align="center" width="150">
					<template slot-scope="{ row }">
						<div>{{ sourceLabels[row.source] }}</div>
						<div>{{ row.category }}</div>
					</template>
				</el-table-column>
				<el-table-column label="摘要" prop="summary" align="center"></el-table-column>
				<el-table-column label="作物品类" prop="cropIds" align="center">
					<template slot-scope="{ row }">
						<el-tag v-for="tag in row.cropTags" :key="tag">{{ tag }}</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="发布时间" prop="publishTime" align="center" width="95">
					<template slot-scope="{ row }">{{
						new Date(row.publishTime).format('yyyy-MM-dd')
					}}</template>
				</el-table-column>
				<el-table-column label="更新时间" prop="updateTime" align="center" width="95">
					<template slot-scope="{ row }">{{
						new Date(row.updateTime).format('yyyy-MM-dd hh:mm:ss')
					}}</template>
				</el-table-column>
				<el-table-column label="操作" align="center" width="110">
					<template slot-scope="{ row }">
						<span class="el_btn_box">
							<el-button size="small" @click="handleInfoDetail(row)">详情</el-button>
						</span>
						<span class="el_btn_box">
							<el-button size="small" @click="handleEditCrop(row)">编辑品类</el-button>
						</span>
						<span class="el_btn_box">
							<el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
						</span>
					</template>
				</el-table-column>
			</el-table>
			<Pagination
				v-if="total > 0"
				:total="total"
				:pageIndex.sync="pageIndex"
				:pageSize.sync="pageSize"
				@pagination="loadData"
			/>
		</div>
		<el-dialog title="编辑资讯作物品类" :visible.sync="editDialogVisible">
			<el-form :model="editForm" ref="editForm" label-width="80px">
				<el-form-item label="作物" prop="cropIds">
					<el-select
						v-model="editForm.cropIds"
						multiple
						placeholder="请选择作物"
						style="width: 100%"
					>
						<el-option
							v-for="crop in cropMaps"
							:key="crop.id"
							:label="crop.name"
							:value="crop.id"
						/>
					</el-select>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button @click="editDialogVisible = false">取 消</el-button>
				<el-button type="primary" :disabled="editLoading" @click="onEditBtnConfirm"
					>确 定</el-button
				>
			</div>
		</el-dialog>
		<el-dialog
			:title="infoDetail.title"
			top="5vh"
			width="80%"
			:visible.sync="infoDetailDialogVisible"
		>
			<div class="news-detail" v-loading="loadingInfoDetail">
				<div class="news-header">
					<h3 class="news-title">{{ infoDetail.title || '--' }}</h3>
					<div class="news-meta">
						<div class="news-source">来源：{{ sourceLabels[infoDetail.source] }}</div>
						<div class="news-source">作者：{{ infoDetail.author || '--' }}</div>
						<div class="news-date">
							发布时间：{{ new Date(infoDetail.publishTime).format('yyyy-MM-dd') }}
						</div>
						<div class="news-source">
							原文链接：
							<a :href="infoDetail.originalLink" target="_blank" rel="noopener noreferrer">
								{{ infoDetail.originalLink || '--' }}
							</a>
						</div>
					</div>
				</div>
				<div v-if="infoDetail.photos" class="news-content">
					<img
						v-for="photo in infoDetail.photos.split(',')"
						:key="photo"
						:src="photo"
						class="info_photo"
						alt="qwc"
					/>
				</div>
				<div v-if="infoDetail.content" class="news-content" v-html="infoDetail.content"></div>
			</div>
		</el-dialog>
		<el-dialog title="更新数据" width="800px" :visible.sync="scrapeDataDialogVisible">
			<div class="scrape-content" v-loading="scrapeLoading">
				<el-button
					v-for="(value, key) in sourceLabels"
					:key="key"
					type="primary"
					icon="el-icon-refresh"
					@click="handleScrape(key)"
					>更新{{ value }}</el-button
				>
			</div>
		</el-dialog>
	</div>
</template>

<script>
	import infoManager from '@/manager/infoManager'
	import Pagination from '@/components/Pagination'
	import tableMixin from '@/mixins/tableMixin'
	export default {
		name: 'InfoList',
		components: { Pagination },
		mixins: [tableMixin],
		data() {
			return {
				sourceLabels: {
					1: '中国农业农村信息网',
					2: '国家粮食交易中心',
					3: '中国大豆产业协会',
					4: '中国棉花协会',
					5: '山东省粮食和物资储备局',
					6: '河北省粮食和物资储备局',
					7: '吉林省粮食和物资储备局',
					8: '新疆维吾尔自治区粮食和物资储备局',
					9: '内蒙古自治区粮食和物资储备局'
				},
				searchParams: {
					title: '',
					cropId: '',
					source: []
				},
				scrapeLoading: false,
				infoDetail: {},
				infoDetailDialogVisible: false,
				loadingInfoDetail: false,
				editDialogVisible: false,
				editForm: {},
				editLoading: false,
				rules: {
					cropIds: [{ required: true, message: '请选择作物品品类', trigger: ['blur', 'change'] }]
				},
				scrapeDataDialogVisible: false
			}
		},
		computed: {
			cropMaps() {
				return Object.values(this.$store.state.cropMap).filter(item => item.parentId == 0)
			}
		},
		methods: {
			clear() {
				this.searchParams = {
					title: '',
					cropId: '',
					source: []
				}
				this.loadData()
			},
			loadData() {
				const {
					searchParams: { title, source, cropId },
					pageIndex,
					pageSize
				} = this

				const params = {
					pageIndex,
					pageSize,
					title,
					cropId,
					source: source.length > 0 ? source.join(',') : ''
				}

				this.loading = true
				infoManager
					.getInfos(params)
					.then(res => {
						const { total, list } = res
						list.forEach(item => {
							const cropTags = []
							const cropIds = item.cropIds ? item.cropIds.split(',') : []
							cropIds.forEach(id => {
								parseInt(id) > 0 && cropTags.push(this.$store.state.cropMap[parseInt(id)].name)
							})
							item.cropTags = cropTags
							return item
						})
						this.total = total
						this.tableData = list
					})
					.catch(err => {
						this.$message.error(err)
					})
					.then(() => {
						this.loading = false
					})
			},
			handleChangeSource(e) {
				this.loadData()
			},
			handleScrape(source) {
				// this.scrapeInfDialogVisible = true
				this.scrapeLoading = true
				infoManager
					.scrapeInfo(source)
					.then(data => {
						this.$alert(
							`本次共爬取了：${data.total}条数据；成功录入：${data.success}条；忽略重复数据：${data.ignore}；录入失败：${data.fail}条`,
							'数据更新成功',
							{
								type: 'success'
							}
						)
						this.loadData()
					})
					.finally(() => {
						this.scrapeLoading = false
					})
			},

			handleDelete(item) {
				this.$confirm(`将删除资讯：${item.title}，是否继续`, '删除提醒', { type: 'warning' }).then(
					() => {
						infoManager
							.deleteInfo(item.id)
							.then(() => {
								this.$message.success('删除成功')
								this.loadData()
							})
							.catch(err => {
								this.$message.error(err)
							})
					}
				)
			},

			handleInfoDetail(item) {
				this.infoDetail = {}
				this.infoDetailDialogVisible = true
				this.loadingInfoDetail = true
				infoManager
					.getInfoDetail(item.id)
					.then(res => {
						this.infoDetail = res
					})
					.finally(() => {
						this.loadingInfoDetail = false
					})
			},

			handleEditCrop(item) {
				this.editDialogVisible = true
				this.editForm = {
					...item,
					cropIds: item.cropIds
						? item.cropIds
								.split(',')
								.map(id => Number(id))
								.filter(num => num != 0)
						: []
				}
			},

			onEditBtnConfirm() {
				this.editLoading = true
				const params = { cropIds: this.editForm.cropIds.join(',') || '0' }
				infoManager
					.updateInfo(this.editForm.id, params)
					.then(() => {
						this.$message.success('更新成功')
						this.editDialogVisible = false
						this.loadData()
						this.editForm = {}
					})
					.finally(() => {
						this.editLoading = false
					})
			}
		}
	}
</script>
<style scoped>
	.search_wrapper {
		display: flex;
		align-items: center;
	}
	::v-deep .el-dialog__body {
		max-height: calc(90vh - 120px);
		/* min-height: 300px; */
		overflow: auto;
	}

	.news-header {
		margin-bottom: 20px;
	}

	.news-title {
		font-size: 18px;
		font-weight: 700;
		margin-bottom: 10px;
		word-break: break-all;
	}

	.news-meta {
		font-size: 14px;
		color: #666;
		line-height: 1.6;
	}

	.news-date {
		margin-right: 10px;
	}

	.news-content {
		overflow: auto;
		/* font-size: 14px;
  line-height: 1.5; */
	}
	.info_photo {
		width: 100%;
	}
	.scrape-content {
		display: flex;
		flex-wrap: wrap;
		justify-content: center;
		.el-button {
			width: 220px;
			margin: 6px;
		}
	}
</style>
