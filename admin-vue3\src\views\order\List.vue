<template>
    <div class="card">
        <div class="search_wrapper">
            <el-select v-model="searchParams.status" placeholder="交易状态" clearable style="width:120px">
                <el-option :value="0" label="求购中"></el-option>
                <el-option :value="1" label="已交易"></el-option>
                <el-option :value="2" label="已取消"></el-option>
            </el-select>
            <el-select v-model="searchParams.infoStatus" placeholder="测量状态" clearable style="width:120px">
                <el-option :value="0" label="未测量"></el-option>
                <el-option :value="1" label="请求测量"></el-option>
                <el-option :value="2" label="已测量"></el-option>
            </el-select>
            <el-input v-model="searchParams.mobile" placeholder="请输入手机" clearable :maxlength="11" style="width:200px"></el-input>
            <el-date-picker
                v-model="searchParams.createTime"
                type="daterange"
                value-format="yyyy-MM-dd HH:mm:ss"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                clearable
                :picker-options="pickerOptions"
            ></el-date-picker>
            <el-button type="primary" @click="loadData">查询</el-button>
            <el-button @click="clear">清空</el-button>
        </div>
        <div class="card-body">
            <el-table v-loading="loading" :data="tableData" border fit highlight-current-row style="width:100%">
                <el-table-column label="ID" prop="id" align="center" width="50" />
                <el-table-column label="作物" align="center" width="70">
                    <template slot-scope="{ row }">{{getCropName(row)}}</template>
                </el-table-column>
                <el-table-column label="手机" prop="mobile" align="center" width="100" />
                <el-table-column label="下单量" prop="count" align="center" width="90" />
                <el-table-column label="是否一口价" prop="fixedPrice" align="center" width="100">
                    <template slot-scope="{ row }">
                        <span>{{ row.fixedPrice ? '是' : '否' }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="交易状态" prop="status" align="center">
                    <template slot-scope="{ row }">
                        <span>{{ statusMap[row.status] }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="测量状态" prop="infoStatus" align="center">
                    <template slot-scope="{ row }">
                        <span>{{ infoStatusMap[row.infoStatus] }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="照片" prop="photos" align="center" width="150">
                    <template slot-scope="{ row }" v-if="row.photos">
                        <el-image class="order_img" fit="contain" v-for="(url,index) in row.photos.split(',')" :key="index" :src="url" :preview-src-list="row.photos.split(',')">
                            <div slot="placeholder" class="image-slot">
                                加载中
                                <span class="dot">...</span>
                            </div>
                        </el-image>
                    </template>
                </el-table-column>
                <el-table-column label="城市" prop="cityCode" align="center" />
                <el-table-column label="地址" prop="address" align="center" />
                <el-table-column label="创建时间" prop="createTime" align="center">
                    <template slot-scope="{ row }">{{ new Date(row.createTime).format('yyyy-MM-dd hh:mm:ss') }}</template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="80">
                    <template slot-scope="{ row }">
                        <span class="el_btn_box">
                            <el-button type="success" size="small" @click="onShowGps(row)">导航</el-button>
                        </span>
                    </template>
                </el-table-column>
            </el-table>
            <Pagination v-if="total > 0" :total="total" :pageIndex.sync="pageIndex" :pageSize.sync="pageSize" @pagination="loadData" />
        </div>

        <el-dialog title="腾讯地图导航链接" :visible.sync="showGpsPage" :close-on-click-modal="false" width="400px">
            <el-input type="textarea" :autosize="{ minRows: 3, maxRows: 5}" readonly v-model="gpsUrl"></el-input>
            <span slot="footer" class="dialog-footer">
                <el-button @click="showGpsPage = false">关 闭</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import orderManager from '@/manager/orderManager'
import Pagination from '@/components/Pagination'
import tableMixin from '@/mixins/tableMixin'
export default {
    name: 'OrderList',
    components: { Pagination },
    mixins: [tableMixin],
    data () {
        return {
            statusMap: {
                0: '求购中',
                1: '已交易',
                2: '已取消'
            },
            infoStatusMap: {
                0: '未测量',
                1: '请求测量',
                2: '已测量'
            },
            pickerOptions: {
                shortcuts: [{
                    text: '今天',
                    onClick (picker) {
                        const start = new Date();
                        const end = new Date();
                        start.setTime(new Date(new Date().toLocaleDateString()).getTime());//今天零点
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '昨天',
                    onClick (picker) {
                        const todayStart = new Date(new Date().toLocaleDateString()).getTime();//今天零点
                        const start = new Date();
                        const end = new Date();
                        start.setTime(todayStart - 3600 * 1000 * 24);
                        end.setTime(todayStart - 1)
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近一周',
                    onClick (picker) {
                        const start = new Date();
                        const end = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近一个月',
                    onClick (picker) {
                        const start = new Date();
                        const end = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近三个月',
                    onClick (picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                    }
                }]
            },
            showGpsPage: false,
            gpsUrl: '',
            searchParams: {
                status: null,
                infoStatus: null,
                mobile: '',
                createTime: null
            }
        }
    },
    methods: {
        clear () {
            this.searchParams = {
                status: null,
                infoStatus: null,
                mobile: '',
                createTime: null
            }
        },
        loadData () {
            const { searchParams: { status, infoStatus, mobile, createTime }, pageIndex, pageSize } = this;
            let dateFrom;
            let dateTo;
            if (createTime) {
                dateFrom = createTime[0];
                dateTo = createTime[1];
            }
            this.loading = true;
            orderManager.queryOrders(status, infoStatus, mobile, dateFrom, dateTo, pageIndex, pageSize).then(res => {
                const { total, list } = res;
                this.total = total;
                this.tableData = list;
                this.loading = false;
            }).catch(err => {
                this.$message.error(err)
                this.loading = false;
            })
        },
        onShowGps (item) {
            // this.gpsUrl = `http://api.map.baidu.com/geocoder?location=${item.latitude},${item.longitude}&output=html&src=zhennongyi&coord_type=gcj02`
            this.gpsUrl = `https://apis.map.qq.com/uri/v1/marker?marker=coord:${item.latitude},${item.longitude};title:${encodeURIComponent(item.crop + item.count + item.countUnit)};&referer=zhongnongyi`
            this.showGpsPage = true;
        }
    }
}
</script>

<style lang="scss" scoped>
.order_img {
    width: 40px;
    height: 40px;
    background-color: #eee;
}
</style>