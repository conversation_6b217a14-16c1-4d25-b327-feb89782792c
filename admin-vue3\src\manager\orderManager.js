import http from '@/utils/http';

function queryCrops (online) {
    return http.post('admin/orderConfig/crop/load', { online })
}

function queryCropCollections () {
    return http.post('admin/orderConfig/collection/load')
}

function addCrop (params) {
    return http.post('admin/orderConfig/crop/add', params)
}

function editCrop (id, params) {
    return http.put('admin/orderConfig/crop/edit/' + id, params)
}

function addCollection (params) {
    return http.post('admin/orderConfig/collection/add', params)
}

function editCollection (id, params) {
    return http.put('admin/orderConfig/collection/edit/' + id, params)
}

function queryOrders (status, infoStatus, mobile, dateFrom, dateTo, pageIndex, pageSize) {
    return http.post('admin/order/load', {
        status, infoStatus, mobile, dateFrom, dateTo, pageIndex, pageSize
    })
}


export default {
    queryCrops,
    queryCropCollections,
    addCrop,
    editCrop,
    addCollection,
    editCollection,
    queryOrders,
}