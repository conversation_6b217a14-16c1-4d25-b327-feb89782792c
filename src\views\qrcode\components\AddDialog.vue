<template>
	<el-dialog title="新增微信群二维码" :visible.sync="visible" width="600px" @close="handleClose">
		<el-form :model="form" :rules="rules" ref="form" label-width="120px">
			<el-form-item label="归属用户" prop="userId">
				<el-autocomplete
					v-model="userSearchText"
					:fetch-suggestions="queryUserSuggestions"
					placeholder="请输入用户名或手机号搜索（留空为系统级二维码）"
					clearable
					style="width: 100%"
					:trigger-on-focus="false"
					@select="handleUserSelect"
					@clear="handleUserClear"
				>
					<template slot-scope="{ item }">
						<div class="user-suggestion-item">
							<span class="user-name">{{ item.name }}</span>
							<span class="user-mobile">{{ item.mobile }}</span>
							<span class="user-type">{{ getUserTypeText(item.type) }}</span>
						</div>
					</template>
				</el-autocomplete>
				<div v-if="selectedUser" class="selected-user-info">
					<el-tag type="success" size="small">
						已选择：{{ selectedUser.name }} ({{ selectedUser.mobile }})
					</el-tag>
				</div>
				<div style="color: #999; font-size: 12px; margin-top: 5px">
					不选择用户则创建系统级二维码
				</div>
			</el-form-item>
			<el-form-item label="二维码图片" prop="qrCodeUrl">
				<el-upload
					class="qrcode-uploader"
					:action="uploadAction"
					:headers="uploadHeaders"
					:show-file-list="false"
					:on-success="handleUploadSuccess"
					:on-error="handleUploadError"
					:before-upload="beforeUpload"
					accept="image/*"
				>
					<img v-if="form.qrCodeUrl" :src="form.qrCodeUrl" class="qrcode-image" />
					<i v-else class="el-icon-plus qrcode-uploader-icon"></i>
				</el-upload>
				<div style="color: #999; font-size: 12px; margin-top: 5px">
					支持 jpg、png 格式，建议尺寸 400x400 像素
				</div>
			</el-form-item>
			<el-form-item label="生效状态" prop="isActive">
				<el-radio-group v-model="form.isActive">
					<el-radio :label="1">生效</el-radio>
					<el-radio :label="0">不生效</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="描述信息" prop="description">
				<el-input
					v-model="form.description"
					type="textarea"
					:rows="3"
					placeholder="请输入二维码描述信息"
					maxlength="500"
					show-word-limit
				></el-input>
			</el-form-item>
		</el-form>
		<div slot="footer" class="dialog-footer">
			<el-button @click="handleCancel">取 消</el-button>
			<el-button type="primary" @click="handleConfirm" :loading="loading">确 定</el-button>
		</div>
	</el-dialog>
</template>

<script>
	import qrcodeManager from '@/manager/qrcodeManager'
	import userManager from '@/manager/userManager'

	export default {
		name: 'AddDialog',
		props: {
			visible: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				loading: false,
				form: {
					userId: null,
					qrCodeUrl: '',
					description: '',
					isActive: 1
				},
				// 用户搜索相关
				userSearchText: '',
				selectedUser: null,
				rules: {
					qrCodeUrl: [{ required: true, message: '请上传二维码图片', trigger: 'change' }],
					isActive: [{ required: true, message: '请选择生效状态', trigger: 'change' }]
				},
				uploadAction: '/api/v1/common/files',
				uploadHeaders: {}
			}
		},
		watch: {
			visible(val) {
				if (val) {
					this.resetForm()
					this.setUploadHeaders()
				}
			}
		},
		methods: {
			// 重置表单
			resetForm() {
				this.form = {
					userId: null,
					qrCodeUrl: '',
					description: '',
					isActive: 1
				}
				this.userSearchText = ''
				this.selectedUser = null
				if (this.$refs.form) {
					this.$refs.form.clearValidate()
				}
			},
			// 设置上传headers
			setUploadHeaders() {
				const token = userManager.getToken()
				if (token) {
					this.uploadHeaders = {
						Authorization: 'Bearer ' + token
					}
				}
			},
			// 查询用户建议
			queryUserSuggestions(queryString, callback) {
				if (!queryString || queryString.length < 1) {
					callback([])
					return
				}

				// 调用用户搜索API
				userManager
					.queryUsers({ name: queryString, pageIndex: 0, pageSize: 10 }) // 搜索前10个匹配的用户
					.then(res => {
						const suggestions = res.list || []
						// 格式化建议数据
						const formattedSuggestions = suggestions.map(user => ({
							...user,
							value: `${user.name} (${user.mobile})` // autocomplete需要value字段
						}))
						callback(formattedSuggestions)
					})
					.catch(err => {
						console.error('搜索用户失败:', err)
						callback([])
					})
			},
			// 用户选择
			handleUserSelect(item) {
				this.selectedUser = item
				this.form.userId = item.id
				this.userSearchText = `${item.name} (${item.mobile})`
			},
			// 清空用户选择
			handleUserClear() {
				this.selectedUser = null
				this.form.userId = null
				this.userSearchText = ''
			},
			// 获取用户类型文本
			getUserTypeText(type) {
				const typeMap = {
					100: '管理员',
					10: '信息收集员',
					1: '收购商',
					2: '粮庄',
					3: '设备商',
					0: '普通用户'
				}
				return typeMap[type] || '普通用户'
			},
			// 上传前验证
			beforeUpload(file) {
				const isImage = file.type.indexOf('image/') === 0
				const isLt2M = file.size / 1024 / 1024 < 2

				if (!isImage) {
					this.$message.error('只能上传图片文件!')
					return false
				}
				if (!isLt2M) {
					this.$message.error('上传图片大小不能超过 2MB!')
					return false
				}
				return true
			},
			// 上传成功回调
			handleUploadSuccess(response) {
				if (response.code === 1) {
					this.form.qrCodeUrl = response.data.url
					this.$message.success('图片上传成功')
				} else {
					this.$message.error(response.message || '上传失败')
				}
			},
			// 上传失败回调
			handleUploadError() {
				this.$message.error('图片上传失败，请重试')
			},
			// 取消
			handleCancel() {
				this.$emit('update:visible', false)
			},
			// 关闭
			handleClose() {
				this.$emit('update:visible', false)
			},
			// 确认新增
			handleConfirm() {
				this.$refs.form.validate(valid => {
					if (valid) {
						this.loading = true
						qrcodeManager
							.createQrcode(this.form)
							.then(() => {
								this.$message.success('新增成功')
								this.$emit('success')
								this.$emit('update:visible', false)
							})
							.catch(err => {
								this.$message.error(err)
							})
							.then(() => {
								this.loading = false
							})
					}
				})
			}
		}
	}
</script>

<style scoped>
	.qrcode-uploader {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
		width: 150px;
		height: 150px;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.qrcode-uploader:hover {
		border-color: #409eff;
	}

	.qrcode-uploader-icon {
		font-size: 28px;
		color: #8c939d;
	}

	.qrcode-image {
		width: 150px;
		height: 150px;
		object-fit: cover;
	}

	.user-suggestion-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 5px 0;
	}

	.user-name {
		font-weight: 500;
		color: #303133;
	}

	.user-mobile {
		color: #909399;
		font-size: 13px;
		margin-left: 10px;
	}

	.user-type {
		color: #409eff;
		font-size: 12px;
		background: #ecf5ff;
		padding: 2px 6px;
		border-radius: 3px;
	}

	.selected-user-info {
		margin-top: 8px;
	}
</style>
