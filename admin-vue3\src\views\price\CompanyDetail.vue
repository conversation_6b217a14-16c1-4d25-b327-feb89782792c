<template>
    <div class="card">
        <div class="search_wrapper">
            <el-input v-model="searchParams.companyId" placeholder="收购公司ID"></el-input>
            <el-select style="width: 140px" v-model="searchParams.cropId" placeholder="请选择作物">
                <el-option v-for="item in cropArr" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
            <el-button type="primary" @click="loadData">查询</el-button>
            <el-button @click="clear">清空</el-button>
            <el-button style="width: 100px;" type="success" icon="el-icon-plus" plain class="fr" @click="onAddClick">新增</el-button>
        </div>
        <div class="card-body">
            <el-table v-loading="loading" :data="tableData" border fit highlight-current-row style="width:100%">
                <el-table-column label="ID" prop="id" align="center" width="50"></el-table-column>
                <el-table-column label="公司ID" prop="companyId" align="center" width="50"></el-table-column>
                <!-- <el-table-column label="收购公司" prop="companyId" align="center" width="120"></el-table-column> -->
                <el-table-column label="收购作物" prop="cropId" align="center" width="100">
                    <template slot-scope="{ row }">{{ getCropName(row) }}</template>
                </el-table-column>
                <el-table-column label="收购标准" prop="desc" align="center">
                    <template slot-scope="{ row }">
                        <div class="pre">{{ row.desc }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="标准图片" prop="photos" align="center" width="100">
                    <template slot-scope="{ row }">
                        <div v-if="row.photos">
                            <el-image v-for="item in row.photos.split(',')" :key="item" :src="item"></el-image>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="创建时间" prop="createTime" align="center" width="95">
                    <template slot-scope="{ row }">{{ new Date(row.createTime).format('yyyy-MM-dd hh:mm:ss') }}</template>
                </el-table-column>
                <el-table-column label="编辑时间" prop="createTime" align="center" width="95">
                    <template slot-scope="{ row }">{{ new Date(row.updateTime).format('yyyy-MM-dd hh:mm:ss') }}</template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="80">
                    <template slot-scope="{ row }">
                        <el-button size="mini" @click="showEdit(row)">编辑</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <Pagination v-if="total > 0" :total="total" :pageIndex.sync="pageIndex" :pageSize.sync="pageSize" @pagination="loadData" />
        </div>
        <el-dialog title="修改报价标准" width="90%" :visible.sync="companyDetailDialogVisible">
            <el-form label-width="110px">
                <!-- <el-form-item label="收购公司">
                    <el-input :value="editCompanyDetailItem.companyId" readonly></el-input>
                </el-form-item>-->
                <el-form-item label="收购作物">
                    <el-select v-model="editCompanyDetailItem.cropId" placeholder="请选择作物">
                        <el-option v-for="item in cropArr" :key="item.id" :label="item.name" :value="item.id"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="收购标准">
                    <el-input type="textarea" :autosize="{ minRows: 3 }" v-model="editCompanyDetailItem.desc" placeholder="请输入收购标准文案"></el-input>
                </el-form-item>
                <el-form-item label="收购标准">
                    <el-upload
                        multiple
                        action="/api/v1/common/upload/photo"
                        :file-list="editFileList"
                        list-type="picture-card"
                        :on-success="handleUploadSuccess"
                        :on-preview="handlePicturePreview"
                        :on-remove="handlePictureRemove"
                    >
                        <i class="el-icon-plus"></i>
                    </el-upload>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="companyDetailDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="onEditDetailBtnConfirm">确 定</el-button>
            </div>
        </el-dialog>
        <el-image-viewer
            class="image_viewer"
            style="z-index: 99999;"
            v-if="showImagePreview"
            :initial-index="previewIndex"
            :url-list="previewImages"
            :on-close="onImageViewerClose"
        />
    </div>
</template>

<script>
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import companyManager from '@/manager/companyManager'
import cropManager from '@/manager/cropManager'
import Pagination from '@/components/Pagination'
import tableMixin from '@/mixins/tableMixin'
export default {
    name: 'CompanyDetailList',
    components: {
        ElImageViewer,
        Pagination
    },
    mixins: [tableMixin],
    data () {
        return {
            companyDetailDialogVisible: false,
            searchParams: {
                companyId: this.$route.query.companyId || null,
                cropId: null
            },
            cropArr: null,
            editCompanyDetailItem: {},
            editFileList: [],
            previewIndex: 0,
            previewImages: [],
            showImagePreview: false
        }
    },
    methods: {
        fetchCropMap () {
            if (this.cropArr) {
                return Promise.resolve()
            }
            return cropManager.queryCrops({ ignoreChildren: 1 }).then(res => {
                this.cropArr = res
            })
        },
        loadData () {
            this.fetchCropMap().then(() => {
                const { searchParams: { companyId, cropId }, pageIndex, pageSize } = this;
                this.loading = true;
                companyManager.queryCompanyDetails(companyId, cropId, pageIndex, pageSize).then(res => {
                    const { total, list } = res;
                    this.total = total;
                    this.tableData = list;
                }).catch(err => {
                    this.$message.error(err)
                }).then(() => {
                    this.loading = false;
                })
            })
        },
        clear () {
            this.searchParams = {
                companyId: '',
                cropId: null
            }
        },
        handlePicturePreview (file) {
            this.showImagePreview = true;
            const arr = [];
            let preIndex = 0;
            this.previewImages = this.editFileList.forEach((item, index) => {
                if (item === file) {
                    preIndex = index;
                }
                arr.push(item.url)
            })
            this.previewImages = arr;
            this.previewIndex = preIndex;
        },
        handleUploadSuccess (response, file, fileList) {
            this.editFileList = fileList
        },
        handlePictureRemove (file, fileList) {
            this.editFileList = fileList
        },
        onImageViewerClose () {
            this.showImagePreview = false;
        },
        onAddClick () {
            this.editFileList = []
            this.editCompanyDetailItem = { companyId: this.searchParams.companyId };
            this.companyDetailDialogVisible = true;
        },
        showEdit (item) {
            this.editCompanyDetailItem = Object.assign({}, item);
            if (this.editCompanyDetailItem.photos) {
                this.editFileList = this.editCompanyDetailItem.photos.split(',').map(url => ({ url }))
            } else {
                this.editFileList = [];
            }
            this.companyDetailDialogVisible = true;
        },
        onEditDetailBtnConfirm () {
            const { id, companyId, cropId, desc } = this.editCompanyDetailItem;
            const photos = [];
            if (this.editFileList) {
                this.editFileList.forEach(item => {
                    photos.push(item.response ? item.response.data : item.url)
                })
            }
            if (id) {
                companyManager.updateCompanyDetail(id, companyId, cropId, desc, photos.join(',')).then(() => {
                    this.$message.success('更新成功')
                    this.companyDetailDialogVisible = false;
                    this.loadData();
                }).catch(err => {
                    this.$message.error(err)
                })
            } else {
                companyManager.addCompanyDetail(companyId, cropId, desc, photos.join(',')).then(() => {
                    this.$message.success('添加成功')
                    this.companyDetailDialogVisible = false;
                    this.loadData();
                }).catch(err => {
                    this.$message.error(err)
                })
            }
        }
    }
}
</script>

<style lang="scss">
.pre {
    white-space: pre;
    text-align: left;
}
.image_viewer {
    .el-icon-circle-close {
        color: #fff;
    }
}
</style>