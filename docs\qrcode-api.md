# 二维码管理 API 接口文档

## 概述

本文档描述了微信群二维码管理系统的后端 API 接口，包括二维码的增删改查、文件上传等功能。

## 基础信息

- **基础 URL**: `/api/v1/admin/qrcode`
- **认证方式**: 需要管理员权限
- **数据格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

### 成功响应

```json
{
	"code": 1,
	"message": "操作成功",
	"data": {}
}
```

### 错误响应

```json
{
	"code": 0,
	"message": "错误信息",
	"data": null
}
```

## API 接口列表

### 1. 查询二维码列表

**接口地址**: `POST /admin/qrcode/load`

**请求参数**:

```json
{
	"userName": "用户名（可选）",
	"userMobile": "用户手机号（可选）",
	"isActive": 1, // 是否生效（可选）：1-生效，0-不生效，null-全部
	"pageIndex": 1, // 页码，从1开始
	"pageSize": 10 // 每页数量
}
```

**响应数据**:

```json
{
	"code": 1,
	"message": "查询成功",
	"data": {
		"total": 100,
		"list": [
			{
				"id": 1,
				"userId": 123, // 用户ID，null表示系统级
				"userName": "张三", // 用户名
				"userMobile": "13800138001", // 用户手机号
				"userType": 1, // 用户类型：100-管理员，10-信息收集员，1-收购商，2-粮庄，3-设备商，0-普通用户
				"qrCodeUrl": "https://example.com/qrcode.jpg", // 二维码图片URL
				"description": "描述信息",
				"isActive": 1, // 是否生效：1-生效，0-不生效
				"createTime": "2024-01-01 12:00:00",
				"updateTime": "2024-01-01 12:00:00"
			}
		]
	}
}
```

### 2. 创建二维码

**接口地址**: `POST /admin/qrcode/create`

**请求参数**:

```json
{
	"userId": 123, // 用户ID（可选，为空表示系统级二维码）
	"qrCodeUrl": "https://example.com/qrcode.jpg", // 二维码图片URL（必填）
	"description": "描述信息", // 描述信息（可选）
	"isActive": 1 // 是否生效：1-生效，0-不生效（必填）
}
```

**响应数据**:

```json
{
	"code": 1,
	"message": "二维码创建成功",
	"data": null
}
```

### 3. 更新二维码

**接口地址**: `PUT /admin/qrcode/update/{id}`

**路径参数**:

- `id`: 二维码 ID

**请求参数**:

```json
{
	"qrCodeUrl": "https://example.com/qrcode.jpg", // 二维码图片URL（可选）
	"description": "描述信息", // 描述信息（可选）
	"isActive": 1 // 是否生效（可选）
}
```

**响应数据**:

```json
{
	"code": 1,
	"message": "二维码更新成功",
	"data": null
}
```

### 4. 删除二维码

**接口地址**: `DELETE /admin/qrcode/delete/{id}`

**路径参数**:

- `id`: 二维码 ID

**响应数据**:

```json
{
	"code": 1,
	"message": "二维码删除成功",
	"data": null
}
```

### 5. 获取二维码详情

**接口地址**: `GET /admin/qrcode/detail/{id}`

**路径参数**:

- `id`: 二维码 ID

**响应数据**:

```json
{
	"code": 1,
	"message": "查询成功",
	"data": {
		"id": 1,
		"userId": 123,
		"userName": "张三",
		"userMobile": "13800138001",
		"userType": 1,
		"qrCodeUrl": "https://example.com/qrcode.jpg",
		"description": "描述信息",
		"isActive": 1,
		"createTime": "2024-01-01 12:00:00",
		"updateTime": "2024-01-01 12:00:00"
	}
}
```

### 6. 获取用户列表

**接口地址**: `GET /admin/qrcode/users`

**响应数据**:

```json
{
	"code": 1,
	"message": "查询成功",
	"data": [
		{
			"id": 123,
			"name": "张三",
			"mobile": "13800138001",
			"type": 1
		}
	]
}
```

### 7. 批量更新二维码状态

**接口地址**: `POST /admin/qrcode/batch-status`

**请求参数**:

```json
{
	"ids": [1, 2, 3], // 二维码ID数组
	"isActive": 1 // 是否生效：1-生效，0-不生效
}
```

**响应数据**:

```json
{
	"code": 1,
	"message": "批量更新成功",
	"data": null
}
```

### 8. 上传二维码图片

**接口地址**: `POST /admin/qrcode/upload`

**请求方式**: `multipart/form-data`

**请求参数**:

- `file`: 图片文件（必填）

**文件限制**:

- 支持格式：jpeg, jpg, png, gif
- 文件大小：最大 2MB
- 存储方式：阿里云 OSS

**响应数据**:

```json
{
	"code": 1,
	"message": "上传成功",
	"data": {
		"url": "https://qwc-prd.oss-cn-hangzhou.aliyuncs.com/qrcode/20241201/qrcode-1640995200000.jpg",
		"filename": "qrcode-1640995200000.jpg",
		"originalname": "my-qrcode.jpg",
		"size": 102400
	}
}
```

## 错误码说明

| 错误码 | 说明     |
| ------ | -------- |
| 0      | 操作失败 |
| 1      | 操作成功 |

## 常见错误信息

- `参数有误` - 请求参数不正确
- `二维码不存在` - 指定的二维码 ID 不存在
- `用户不存在` - 指定的用户 ID 不存在
- `请选择要更新的二维码` - 批量操作时未提供 ID 数组
- `只允许上传图片文件` - 上传的文件格式不支持
- `文件大小不能超过2MB` - 上传的文件过大
- `请选择要上传的文件` - 未选择上传文件

## 数据库表结构

### t_qrcode 表

| 字段名      | 类型         | 说明                        |
| ----------- | ------------ | --------------------------- |
| id          | int(11)      | 主键 ID                     |
| user_id     | int(11)      | 归属用户 ID，为空表示系统级 |
| user_name   | varchar(50)  | 用户名（冗余字段）          |
| user_mobile | varchar(20)  | 用户手机号（冗余字段）      |
| user_type   | int(11)      | 用户类型（冗余字段）        |
| qr_code_url | varchar(500) | 二维码图片 URL              |
| description | text         | 描述信息                    |
| is_active   | tinyint(1)   | 是否生效：1-生效，0-不生效  |
| create_time | timestamp    | 创建时间                    |
| update_time | timestamp    | 更新时间                    |
| create_by   | int(11)      | 创建人 ID                   |
| update_by   | int(11)      | 更新人 ID                   |

## 注意事项

1. 所有接口都需要管理员权限认证
2. 文件上传接口支持的图片格式有限，请确保上传正确的文件类型
3. 批量操作时请注意 ID 数组不能为空
4. 系统级二维码的 userId 为 null
5. 用户相关字段（userName、userMobile、userType）在创建时会自动从用户表获取并冗余存储
