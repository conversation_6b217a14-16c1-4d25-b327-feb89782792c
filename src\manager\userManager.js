import md5 from 'md5'
import http from '@/utils/http'
import storeManager from '@/utils/storeManager'
const managerKey = 'zny_user_manager'

let token = storeManager.get('token', manager<PERSON>ey)
let admin = storeManager.get('admin', manager<PERSON>ey)

function getToken() {
	return token
}

function getAdmin() {
	return admin
}

function login(mobile, password) {
	return http.post('login', { mobile, password: md5(password) }).then(user => {
		token = user.token
		admin = user
		storeManager.store('token', managerKey, token)
		storeManager.store('admin', managerKey, user)
		return user
	})
}

function logout() {
	clearUser()
	return Promise.resolve()
}

// 用户登录时缓存用户登录信息，下次可以直接不用输入
function rememberAdmin(mobile, password) {
	storeManager.store(
		'login_remember',
		managerKey,
		{
			mobile,
			password
		},
		'1000d'
	) //让这个缓存长时间有效
}

// 获取本地缓存用户登录信息
function getRememberAdmin() {
	const data = storeManager.get('login_remember', manager<PERSON><PERSON>)
	return new Promise(resolve => {
		if (data) {
			resolve(data)
		}
	}).catch(err => err)
}

// 清除本地缓存用户登录信息
function clearRememberAdmin() {
	storeManager.clear('login_remember', managerKey)
}

function clearUser() {
	token = null
	admin = null
	storeManager.clear('token', managerKey)
	storeManager.clear('admin', managerKey)
}

function getLoginTime() {
	const { updateTime } = storeManager.getIgnoreMaxAge('token', managerKey)
	return updateTime
}

function queryUsers(mobile, name, address, type, pageIndex, pageSize) {
	return http.post('admin/user/load', {
		mobile,
		name,
		address,
		type,
		pageIndex,
		pageSize
	})
}

function updateUser(id, params) {
	return http.put('admin/user/update/' + id, params)
}

function loadSystemUsers() {
	return http.get('admin/system/user/load')
}

export default {
	getToken,
	getAdmin,
	login,
	logout,
	clearUser,
	rememberAdmin,
	getRememberAdmin,
	clearRememberAdmin,
	getLoginTime,
	queryUsers,
	updateUser,
	loadSystemUsers
}
