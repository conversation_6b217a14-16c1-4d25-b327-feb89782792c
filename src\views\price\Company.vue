<template>
	<div class="card">
		<div class="search_wrapper">
			<el-cascader
				:style="{ width: '180px' }"
				v-model="searchParams.area"
				placeholder="请选择区域"
				:options="$store.state.areaArr"
				:props="{
					value: 'code',
					label: 'name',
					checkStrictly: true
				}"
				clearable
			></el-cascader>
			<el-input v-model="searchParams.name" placeholder="收购厂名称" clearable></el-input>
			<el-button type="primary" @click="loadData">查询</el-button>
			<el-button @click="clear">清空</el-button>
			<el-button class="add_btn" type="primary" plain icon="el-icon-plus" @click="showCompanyAdd"
				>新增</el-button
			>
		</div>
		<div class="card-body">
			<el-table
				v-loading="loading"
				:data="tableData"
				border
				fit
				highlight-current-row
				style="width: 100%"
			>
				<el-table-column label="ID" prop="id" align="center" width="50"></el-table-column>
				<el-table-column label="名字" prop="name" align="center"></el-table-column>
				<el-table-column label="计入平均值" prop="level" align="center" width="100">
					<template slot-scope="{ row }">
						<el-tag size="mini" type="success" v-if="row.level">计入</el-tag>
						<el-tag size="mini" type="info" v-else>不计入</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="所在区域" prop="cityCode" align="center">
					<template slot-scope="{ row }">{{ cityCodeToText(row.cityCode) }}</template>
				</el-table-column>
				<el-table-column label="创建时间" prop="createTime" align="center">
					<template slot-scope="{ row }">{{
						new Date(row.createTime).format('yyyy-MM-dd hh:mm:ss')
					}}</template>
				</el-table-column>
				<el-table-column label="操作" align="center" width="120">
					<template slot-scope="{ row }">
						<div>
							<el-button
								type="success"
								size="mini"
								@click="$router.push(`companyDetail?companyId=${row.id}`)"
								>收购标准</el-button
							>
						</div>
						<div style="margin-top: 10px">
							<el-button type="primary" size="mini" @click="showCompanyEdit(row)"
								>编辑企业</el-button
							>
						</div>
						<div style="margin-top: 10px">
							<el-button size="mini" @click="showPriceInput(row)">录入价格</el-button>
						</div>
					</template>
				</el-table-column>
			</el-table>
			<Pagination
				v-if="total > 0"
				:total="total"
				:pageIndex.sync="pageIndex"
				:pageSize.sync="pageSize"
				@pagination="loadData"
			/>
		</div>
		<el-dialog
			:title="editCompany.id ? '编辑收购厂家' : '新增收购厂家'"
			:visible.sync="companyDialogVisible"
		>
			<el-form label-width="100px">
				<el-form-item label="公司名称">
					<el-input v-model="editCompany.name" placeholder="请输入收购厂家名称"></el-input>
				</el-form-item>
				<el-form-item label="计入平均值">
					<el-radio v-model="editCompany.level" :label="1">计入平均值</el-radio>
					<el-radio v-model="editCompany.level" :label="0">不计入平均值</el-radio>
				</el-form-item>
				<el-form-item label="所在区域">
					<el-cascader
						:style="{ width: '100%' }"
						v-model="editCompany.area"
						placeholder="请选择区域"
						:options="$store.state.areaArr"
						:props="{
							value: 'code',
							label: 'name',
							checkStrictly: true
						}"
						clearable
					></el-cascader>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button @click="companyDialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="onCompanyEditConfirm">确 定</el-button>
			</div>
		</el-dialog>
		<el-dialog title="新增报价信息" :visible.sync="priceDialogVisible">
			<el-form label-width="110px">
				<el-form-item label="收购公司">
					<el-input :value="editPrice.companyName" readonly></el-input>
				</el-form-item>
				<el-form-item label="收购作物">
					<el-select
						v-model="editPrice.cropId"
						placeholder="请选择作物"
						style="width: 100%"
						@change="handleCropChange"
					>
						<el-option
							v-for="item in crops"
							:key="item.id"
							:label="item.name"
							:value="item.id"
						></el-option>
					</el-select>
				</el-form-item>
				<el-row :gutter="24">
					<el-col :span="12">
						<el-form-item label="报价">
							<el-input-number v-model="editPrice.price" :precision="4" :step="0.1" />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="报价单位">
							<span style="font-size: 15; font-weight: 600">{{ editPrice.unit }}</span>
						</el-form-item>
					</el-col>
				</el-row>

				<el-form-item label="报价日期">
					<el-date-picker
						v-model="editPrice.date"
						type="date"
						value-format="yyyy-MM-dd"
						placeholder="选择日期"
					></el-date-picker>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button @click="priceDialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="onPriceAddConfirm">确 定</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
	import companyManager from '@/manager/companyManager'
	import cropPriceManager from '@/manager/cropPriceManager'
	import cropManager from '@/manager/cropManager'
	import Pagination from '@/components/Pagination'
	import tableMixin from '@/mixins/tableMixin'
	import areaMixin from '@/mixins/areaMixin'
	export default {
		name: 'CompanyList',
		data() {
			return {
				companyDialogVisible: false,
				priceDialogVisible: false,
				searchParams: {
					area: null,
					name: null,
					cropId: null
				},
				crops: [],
				editCompany: {
					id: null,
					name: null,
					cropId: 1,
					level: 1,
					area: null
				},
				editPrice: {
					companyName: null,
					companyId: null,
					cropId: null,
					price: null,
					unit: '',
					date: null
				}
			}
		},
		components: { Pagination },
		mixins: [tableMixin, areaMixin],
		mounted() {
			this.fetchCrops()
		},
		methods: {
			loadData() {
				const {
					searchParams: { area, cropId, name },
					pageIndex,
					pageSize
				} = this
				let cityCode = null
				if (area && area.length > 0) {
					cityCode = area[area.length - 1]
				}
				this.loading = true
				companyManager
					.queryCompanies(cityCode, cropId, name, pageIndex, pageSize)
					.then(res => {
						const { total, list } = res
						this.total = total
						this.tableData = list.map(item => {
							if (item.detail) {
								try {
									item.detailArr = JSON.parse(
										item.detail.replace(/\n/g, '\\n').replace(/\r/g, '\\r')
									)
								} catch (error) {
									console.log(error)
								}
							} else {
								item.detailArr = []
							}
							return item
						})
					})
					.catch(err => {
						this.$message.error(err)
					})
					.then(() => {
						this.loading = false
					})
			},
			fetchCrops() {
				cropManager.queryCrops({ ignoreChildren: 1 }).then(res => {
					this.crops = res
				})
			},
			clear() {
				this.searchParams.area = null
				this.searchParams.cropId = null
				this.searchParams.name = null
			},
			showCompanyAdd() {
				if (this.editCompany.id) {
					//先前的操作是编辑，清空数据
					this.editCompany = {
						id: null,
						name: null,
						cropId: 1,
						level: 1,
						area: null,
						detail1: null, //玉米报价详情
						detail2: null //小麦报价详情
					}
				}
				this.companyDialogVisible = true
			},
			showCompanyEdit(row) {
				const { id, cityCode, cropId, level, name } = row
				Object.assign(this.editCompany, { id, cityCode, cropId, level, name })
				this.editCompany.area = this.cityCodeToSelectorValue(cityCode)
				this.companyDialogVisible = true
			},
			onCompanyEditConfirm() {
				const { id, area, level, name } = this.editCompany
				if (!area || area.length === 0) {
					this.$message.error('请选择区域位置')
					return
				}
				if (!name) {
					this.$message.error('请输入企业名称')
					return
				}
				const cityCode = area[area.length - 1]
				this.loading = true
				const task = id
					? companyManager.updateCompany(id, cityCode, level, name)
					: companyManager.addCompany(cityCode, level, name)
				task
					.then(data => {
						this.companyDialogVisible = false
						this.$message.success(data)
						this.loadData()
					})
					.catch(err => {
						this.$message.error(err)
						this.loading = false
					})
			},
			showPriceInput(row) {
				const { id, name, cropId } = row
				Object.assign(this.editPrice, {
					companyId: id,
					companyName: name,
					cropId,
					date: new Date().format('yyyy-MM-dd')
				})
				this.handleCropChange(cropId)
				this.priceDialogVisible = true
			},

			handleCropChange(id) {
				this.editPrice.unit = this.crops.find(item => item.id === id)?.priceUnit
			},
			onPriceAddConfirm() {
				const { cropId, companyId, companyName, price, unit, date } = this.editPrice
				if (!cropId) {
					this.$message.error('请选择农作物')
				} else if (!price) {
					this.$message.error('请录入收购价')
				} else if (!date) {
					this.$message.error('选择保价日期')
				} else {
					this.loading = true
					cropPriceManager
						.addPrice(cropId, companyId, companyName, price, unit, date)
						.then(data => {
							this.$message.success(data)
							this.loading = false
							this.priceDialogVisible = false
						})
						.catch(err => {
							this.$message.error(err)
						})
						.then(() => {
							this.loading = false
						})
				}
			}
		}
	}
</script>
