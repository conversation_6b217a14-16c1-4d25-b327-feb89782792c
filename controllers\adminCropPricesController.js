// 后台数据列表数据查询模块
const cropCompanyController = require('./cropCompanyController');
const cropPortDailyController = require('./cropPortDailyController');
const cropPriceController = require('./cropPriceController');
const dbController = require('./dbController')
const { resFilter } = require('../utils/filter')
const { PRICE_DAILY } = require('../db').tableNames;


function queryCropCompanies (cityCode, name, pageIndex, pageSize) {
    return new Promise(function (resolve, reject) {
        cropCompanyController.countCompanies(cityCode, name).then(total => {
            if (total > 0) {
                cropCompanyController.queryCompanies(cityCode, name, pageIndex, pageSize).then(list => {
                    resolve({
                        list,
                        total
                    })
                }).catch(err => {
                    reject(err)
                })
            } else {
                resolve({
                    list: [],
                    total: 0
                })
            }
        }).catch(err => {
            reject(err)
        })
    })
}

function queryCropPrices (companyName, cropId, date, pageIndex, pageSize) {
    return new Promise(function (resolve, reject) {
        const queryOptions = {
            'companyName.like': companyName,
            cropId,
            date
        }
        dbController.count(PRICE_DAILY, queryOptions, (err, rows) => {
            if (err) {
                reject(err);
            } else {
                const total = rows[0]['count(*)'];
                if (total > 0) {
                    dbController.query(PRICE_DAILY, queryOptions, { pageIndex, pageSize, orderBy: 'id', orderSort: 'DESC' }, (err, rows) => {
                        if (err) {
                            reject(err)
                        } else {
                            resolve({
                                total,
                                list: rows.map(item => resFilter(item))
                            })
                        }
                    })
                } else {
                    resolve({
                        list: [],
                        total: 0
                    })
                }
            }
        })
    })
}

// 查询每日港口信息等
function queryCropOthers (date, pageIndex, pageSize) {
    return new Promise(function (resolve, reject) {
        cropPortDailyController.countDaily(date).then(total => {
            if (total > 0) {
                cropPortDailyController.queryDaily(date, pageIndex, pageSize).then(list => {
                    resolve({
                        list,
                        total
                    })
                }).catch(err => {
                    reject(err)
                })
            } else {
                resolve({
                    list: [],
                    total: 0
                })
            }
        }).catch(err => {
            reject(err)
        })
    })
}

module.exports = {
    queryCropCompanies,
    queryCropPrices,
    queryCropOthers
}