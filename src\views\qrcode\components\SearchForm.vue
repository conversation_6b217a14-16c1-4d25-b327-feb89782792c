<template>
	<div class="search_wrapper">
		<el-input
			v-model="searchParams.userName"
			placeholder="请输入用户名"
			clearable
			style="width: 200px"
			@keyup.enter.native="handleSearch"
		></el-input>
		<el-input
			v-model="searchParams.userMobile"
			placeholder="请输入用户手机号"
			clearable
			:maxlength="11"
			style="width: 200px"
			@keyup.enter.native="handleSearch"
		></el-input>
		<el-select 
			v-model="searchParams.isActive" 
			placeholder="生效状态" 
			clearable 
			style="width: 120px"
		>
			<el-option :value="1" label="生效"></el-option>
			<el-option :value="0" label="不生效"></el-option>
		</el-select>
		<el-button type="primary" @click="handleSearch">查询</el-button>
		<el-button @click="handleClear">清空</el-button>
		<el-button type="success" @click="handleAdd">新增二维码</el-button>
	</div>
</template>

<script>
export default {
	name: 'SearchForm',
	props: {
		value: {
			type: Object,
			default: () => ({
				userName: '',
				userMobile: '',
				isActive: null
			})
		}
	},
	data() {
		return {
			searchParams: {
				userName: '',
				userMobile: '',
				isActive: null
			}
		}
	},
	watch: {
		value: {
			handler(val) {
				this.searchParams = { ...val }
			},
			immediate: true,
			deep: true
		},
		searchParams: {
			handler(val) {
				this.$emit('input', val)
			},
			deep: true
		}
	},
	methods: {
		// 搜索
		handleSearch() {
			this.$emit('search', this.searchParams)
		},
		// 清空
		handleClear() {
			this.searchParams = {
				userName: '',
				userMobile: '',
				isActive: null
			}
			this.$emit('clear')
		},
		// 新增
		handleAdd() {
			this.$emit('add')
		}
	}
}
</script>

<style scoped>
.search_wrapper {
	display: flex;
	align-items: center;
	gap: 10px;
	margin-bottom: 20px;
	flex-wrap: wrap;
}

.search_wrapper .el-input,
.search_wrapper .el-select {
	margin-right: 10px;
}

.search_wrapper .el-button {
	margin-right: 10px;
}

@media (max-width: 768px) {
	.search_wrapper {
		flex-direction: column;
		align-items: stretch;
	}
	
	.search_wrapper .el-input,
	.search_wrapper .el-select,
	.search_wrapper .el-button {
		margin-right: 0;
		margin-bottom: 10px;
		width: 100%;
	}
}
</style>
