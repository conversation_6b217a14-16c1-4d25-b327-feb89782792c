import axios from 'axios';
import qs from 'qs';
import { Message, MessageBox } from 'element-ui';
import store from '../store'

// errIgnore 是否由请求来处理请求失败
function http (method, url, data, errIgnore) {
    // 对于 undefined | null | '' 当作无效传参抛弃
    for (const key in data) {
        const value = data[key];
        if (value === undefined || value === null || value === '') {
            delete data[key]
        }
    }
    method = method.toLowerCase();
    url = `/api/v1/${url}`;
    if (method === 'get' || method === 'delete') {
        return requestEnd(axios[method](url, { params: data }), errIgnore);
    } else if (method === 'post' || method === 'put') {
        return requestEnd(axios[method](url, qs.stringify(data)), errIgnore);
    }
}

function requestEnd (httpPromise, errIgnore) {
    return new Promise(function (resolve, reject) {
        httpPromise.then(res => {
            if (res.data) {
                if (res.data.code === 1) {
                    resolve(res.data.data);
                } else if (res.data.code === -1) {
                    MessageBox.alert('您的登录信息已失效，请重新登录', '提示', {
                        callback: () => {
                            store.commit('LOGOUT');
                        }
                    })
                } else if (errIgnore) {
                    Message.error(res.data.message);
                } else {
                    reject(res.data);
                }
            } else if (errIgnore) {
                Message.error('请求异常');
            } else {
                reject('请求异常');
            }
        }, err => {
            console.error(err);
            if (errIgnore) {
                Message.error('网络异常');
            } else {
                reject('网络异常', err);
            }
        })
    })
}

['get', 'post', 'put', 'delete'].forEach(type => {
    http[type] = function (url, data, errIgnore) {
        return http(type, url, data, errIgnore);
    }
})

export default http;