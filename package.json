{"name": "qianwancang-backend", "version": "1.0.0", "private": true, "scripts": {"start": "node ./bin/www", "dev": "nodemon", "pro": "export NODE_ENV=production && node ./bin/www"}, "dependencies": {"ali-oss": "^6.21.0", "axios": "^1.2.1", "cheerio": "1.0.0-rc.9", "cookie-parser": "^1.4.6", "cookie-session": "^2.0.0", "crypto-js": "^4.2.0", "debug": "~2.6.9", "dotenv": "^16.4.5", "entities": "^5.0.0", "excel-export": "^0.5.1", "express": "~4.16.1", "express-art-template": "^1.0.1", "formidable": "^2.1.1", "http-errors": "~1.6.3", "iconv-lite": "^0.6.3", "jade": "~1.11.0", "md5": "^2.3.0", "morgan": "~1.9.1", "mysql": "^2.18.1", "node-cache": "^5.1.2", "nodemailer": "^6.9.3", "sha1": "^1.1.1", "tesseract.js": "^4.1.1", "uuid": "^9.0.0"}, "devDependencies": {"nodemon": "^3.1.7"}}