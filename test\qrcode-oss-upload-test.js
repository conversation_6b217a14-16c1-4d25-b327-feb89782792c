/**
 * 二维码OSS上传功能测试脚本
 * 
 * 使用方法：
 * 1. 确保服务器已启动
 * 2. 确保阿里云OSS配置正确
 * 3. 准备一个测试图片文件
 * 4. 运行 node test/qrcode-oss-upload-test.js
 */

const axios = require('axios')
const FormData = require('form-data')
const fs = require('fs')
const path = require('path')

// 配置
const API_BASE_URL = 'http://localhost:3000/api/v1'
const ADMIN_TOKEN = 'your_admin_token_here' // 替换为实际的管理员token

// 创建axios实例
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Authorization': `Bearer ${ADMIN_TOKEN}`
  }
})

// 测试函数
async function testQrcodeOSSUpload() {
  console.log('开始测试二维码OSS上传功能...')
  
  try {
    // 测试1: 上传有效的图片文件
    console.log('\n测试1: 上传有效的图片文件')
    await testValidImageUpload()
    
    // 测试2: 测试文件大小限制
    console.log('\n测试2: 测试文件大小限制')
    await testFileSizeLimit()
    
    // 测试3: 测试文件类型验证
    console.log('\n测试3: 测试文件类型验证')
    await testFileTypeValidation()
    
    // 测试4: 测试无文件上传
    console.log('\n测试4: 测试无文件上传')
    await testNoFileUpload()
    
    console.log('\n所有测试完成!')
    
  } catch (error) {
    console.error('测试过程中出错:', error.message)
  }
}

// 测试有效图片上传
async function testValidImageUpload() {
  try {
    // 创建一个小的测试图片（1x1像素的PNG）
    const testImageBuffer = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
      0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
      0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
      0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0x99, 0x01, 0x01, 0x00, 0x00, 0x00,
      0xFF, 0xFF, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01, 0xE2, 0x21, 0xBC, 0x33,
      0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
    ])
    
    const formData = new FormData()
    formData.append('file', testImageBuffer, {
      filename: 'test-qrcode.png',
      contentType: 'image/png'
    })
    
    const response = await axios.post(
      `${API_BASE_URL}/admin/qrcode/upload`,
      formData,
      {
        headers: {
          ...formData.getHeaders(),
          'Authorization': `Bearer ${ADMIN_TOKEN}`
        }
      }
    )
    
    console.log('✅ 有效图片上传成功')
    console.log('状态码:', response.status)
    console.log('返回数据:', response.data.data)
    
    // 验证返回的URL是否为OSS地址
    if (response.data.data.url.includes('oss-cn-hangzhou.aliyuncs.com')) {
      console.log('✅ 确认文件已上传到阿里云OSS')
    } else {
      console.log('❌ 文件未上传到OSS，URL:', response.data.data.url)
    }
    
  } catch (error) {
    console.error('❌ 有效图片上传失败:', error.response?.data || error.message)
  }
}

// 测试文件大小限制
async function testFileSizeLimit() {
  try {
    // 创建一个超过2MB的文件
    const largeBuffer = Buffer.alloc(3 * 1024 * 1024, 0) // 3MB
    
    const formData = new FormData()
    formData.append('file', largeBuffer, {
      filename: 'large-file.png',
      contentType: 'image/png'
    })
    
    const response = await axios.post(
      `${API_BASE_URL}/admin/qrcode/upload`,
      formData,
      {
        headers: {
          ...formData.getHeaders(),
          'Authorization': `Bearer ${ADMIN_TOKEN}`
        }
      }
    )
    
    console.log('❌ 大文件上传应该失败但成功了')
    
  } catch (error) {
    if (error.response?.data?.message?.includes('2MB')) {
      console.log('✅ 文件大小限制正常工作')
    } else {
      console.error('❌ 文件大小限制测试失败:', error.response?.data || error.message)
    }
  }
}

// 测试文件类型验证
async function testFileTypeValidation() {
  try {
    // 创建一个文本文件
    const textBuffer = Buffer.from('This is not an image file')
    
    const formData = new FormData()
    formData.append('file', textBuffer, {
      filename: 'test.txt',
      contentType: 'text/plain'
    })
    
    const response = await axios.post(
      `${API_BASE_URL}/admin/qrcode/upload`,
      formData,
      {
        headers: {
          ...formData.getHeaders(),
          'Authorization': `Bearer ${ADMIN_TOKEN}`
        }
      }
    )
    
    console.log('❌ 非图片文件上传应该失败但成功了')
    
  } catch (error) {
    if (error.response?.data?.message?.includes('图片文件')) {
      console.log('✅ 文件类型验证正常工作')
    } else {
      console.error('❌ 文件类型验证测试失败:', error.response?.data || error.message)
    }
  }
}

// 测试无文件上传
async function testNoFileUpload() {
  try {
    const formData = new FormData()
    // 不添加任何文件
    
    const response = await axios.post(
      `${API_BASE_URL}/admin/qrcode/upload`,
      formData,
      {
        headers: {
          ...formData.getHeaders(),
          'Authorization': `Bearer ${ADMIN_TOKEN}`
        }
      }
    )
    
    console.log('❌ 无文件上传应该失败但成功了')
    
  } catch (error) {
    if (error.response?.data?.message?.includes('未检测到有效文件')) {
      console.log('✅ 无文件上传验证正常工作')
    } else {
      console.error('❌ 无文件上传验证测试失败:', error.response?.data || error.message)
    }
  }
}

// 运行测试
testQrcodeOSSUpload()
