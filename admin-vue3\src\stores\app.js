import { defineStore } from 'pinia'

export const useAppStore = defineStore('app', {
  state: () => ({
    collapseMenu: true,
    cropArr: [],
    cropMap: {},
    areaArr: []
  }),
  
  getters: {
    getCropById: (state) => (id) => state.cropMap[id],
    getAllCrops: (state) => state.cropArr,
    getAllAreas: (state) => state.areaArr,
    isMenuCollapsed: (state) => state.collapseMenu
  },
  
  actions: {
    toggleSidebarCollapse() {
      this.collapseMenu = !this.collapseMenu
    },
    
    setCrops(crops) {
      const cropMap = {}
      crops.forEach(item => {
        cropMap[item.id] = item
      })
      this.cropArr = crops
      this.cropMap = cropMap
    },
    
    setAreas(areas) {
      this.areaArr = areas
    }
  }
})