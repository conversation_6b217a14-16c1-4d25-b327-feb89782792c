const express = require('express')
const router = express.Router()
const commonController = require('../controllers/commonController')
const cityController = require('../controllers/cityController')
const cropController = require('../controllers/cropController')
const systemController = require('../controllers/systemController')
const wxCommonApi = require('../utils/wxCommonApi')
const axios = require('axios')
const qs = require('qs')
var nodeExcel = require('excel-export')

router.get('/crop/config', function (req, res) {
	const task1 = cropController.queryCrops(1)
	const task2 = cropController.queryCollections()
	Promise.all([task1, task2])
		.then(resArr => {
			res.sendSuccess({
				crops: resArr[0],
				collections: resArr[1]
			})
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

// 获取全部的作物分类，包括价格页的分类
router.get('/crop/all', function (req, res) {
	const task1 = cropController.queryCrops()
	const task2 = cropController.queryCollections()
	Promise.all([task1, task2])
		.then(resArr => {
			res.sendSuccess({
				crops: resArr[0],
				collections: resArr[1]
			})
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.get('/sys/config', function (req, res) {
	systemController
		.queryConfig()
		.then(data => {
			if (data && data.length > 0) {
				delete data[0].id
				res.sendSuccess(data[0])
			} else {
				res.sendMessage('系统配置信息为空')
			}
		})
		.catch(err => {
			res.sendMessage(err.stack)
		})
})

router.post('/files/wx-uploads', (req, res) => {
	commonController
		.uploadFileToOSS(req)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			console.log(err)
			res.sendMessage(err)
		})
})

/**
 * 上传文件到OSS存储服务
 * @param {import('express').Request} req - Express请求对象，包含上传的文件数据
 * @param {import('express').Response} res - Express响应对象，用于返回处理结果
 */
router.post('/files', async (req, res) => {
	try {
		const data = await commonController.uploadFileToOSS(req)
		res.sendSuccess(data)
	} catch (err) {
		console.log(err)
		res.sendMessage(err)
	}
})

// router.post('/files/local-uploads', (req, res) => {
// 	/* 本地文件上传逻辑 */
// })
// router.post('/files/remote-uploads', (req, res) => {
// 	/* 远程文件上传逻辑 */
// })

// 上次图片
// TODO: 后续需要将图片存储迁移到阿里云
router.post('/upload/photo', function (req, res) {
	commonController
		.uploadPhoto(req)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			console.log(err)
			res.sendMessage(err)
		})
})

// 上次视频
// TODO: 后续需要将视频存储迁移到阿里云
router.post('/upload/video', function (req, res) {
	commonController
		.uploadVideo(req)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			console.log(err)
			res.sendMessage(err)
		})
})

// 加载全国地图
router.get('/loadAllCities', function (req, res) {
	cityController
		.queryAllCities(req)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

// router.get('/loadImgInfo', function (req, res) {
//     const url = req.query.url;
//     ocrController.getImgInfo(url).then(data => {
//         res.sendSuccess(data)
//     }).catch(err => {
//         res.sendMessage(err)
//     })
// })

// 对腾讯地图的经纬度坐标进行逆向解析
router.get('/address/geocoder', function (req, res) {
	const { latitude, longitude } = req.query
	wxCommonApi
		.geocoderAddress(latitude, longitude)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

// 51 job后门拉取简历
router.post('/51job/backdoor2', (httpReq, httpRes) => {
	let { id, pageNumber, viewState, cookie } = httpReq.body
	const headers = {
		'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8',
		Host: 'ehire.51job.com',
		Origin: 'https://ehire.51job.com',
		'sec-ch-ua': `" Not A;Brand";v="99", "Chromium";v="101", "Google Chrome";v="101"`,
		'sec-ch-ua-mobile': `?0`,
		'sec-ch-ua-platform': 'macOS',
		'Sec-Fetch-Dest': 'document',
		'Sec-Fetch-Mode': 'navigate',
		'Sec-Fetch-User': '?1',
		'Upgrade-Insecure-Requests': 1,
		'User-Agent':
			'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.64 Safari/537.36',
		Cookie: cookie
	}
	axios({
		method: 'post',
		url: `https://ehire.51job.com/InboxResume/InboxViewEngine.aspx?JobID=${id}`,
		headers,
		data: qs.stringify({
			__EVENTTARGET: `pagerBottomNew$btnNum${pageNumber}`,
			pagerTopNew$ctl06: 50,
			__VIEWSTATE: viewState,
			rdbCustomize: 'on',
			ctlSearchInboxEngine1$hid_keywordtype: 15,
			ctlSearchInboxEngine1$hid_allTxt: 0,
			ctlSearchInboxEngine1$hidShowMore: 0,
			ctlSearchInboxEngine1$hidmorelistheight: 84,
			hidShowCode: 0,
			hidIsFilter: 0,
			HidJobIds: id,
			HidJobValues: id,
			hid_isafreshsearch: 1,
			hid_posttime: 180,
			hidShowMore: 0,
			hidDisplayType: 0,
			hidFolder: 'EMP',
			hidJobID: id,
			hidRefresh: 0,
			exportType: 'Word',
			downloadType: 0,
			screen: 0,
			chk_intvplan: 'on',
			intvresult: 1,
			chk_offerplan: 'on',
			offerresult: 1
		})
	})
		.then(res => {
			let html = res.data
			let arr = html.split(`a_username' title='`)
			arr.shift()
			// arr 的元素 是每一个用户的列
			let listArr = arr.map(item => {
				let obj = {}
				let nameEnd = item.indexOf(`'`)
				let linkStart = item.indexOf(`='`) + 2
				let linkEnd = item.indexOf(`'>`)
				obj.name = item.substr(0, nameEnd)
				const link = item.substring(linkStart, linkEnd)
				obj.link = `https://ehire.51job.com${link}`
				// 第二个切割点
				let arr2 = item.split(`<td class="inbox_td" onclick="EHRResumeList.SetTrDetailDisplayOff`)
				arr2.shift()
				// 依次爬取年龄、工作年限、性别、学历、职能、投递时间 字段
				arr2.forEach((item2, index) => {
					let start = item2.indexOf('>') + 1
					let end = item2.indexOf('<')
					let keyMap = ['age', 'workAge', 'gender', 'xueLi', 'jobAbility', 'sendTime']
					obj[keyMap[index]] = item2.substring(start, end)
				})
				return obj
			})
			if (listArr.length > 0) {
				loadAllUsers(headers, listArr, newArr => {
					data2Excel(newArr, httpRes)
				})
			} else {
				resSend(httpRes, `网页爬取失败，请尝试刷新前程无忧页面后，重新配置参数`, 0)
			}
		})
		.catch(err => {
			console.log(err)
			resSend(httpRes, err, 0)
		})
})
// 51 job后门拉取简历
router.post('/51job/backdoor', (httpReq, httpRes) => {
	const { id, userIds, cookie } = httpReq.body
	const headers = {
		'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8',
		Host: 'ehire.51job.com',
		Cookie: cookie
	}
	if (userIds) {
		const urlArr = userIds.split(',').map(uId => `hidSeqID=${uId}&hidFolder`)
		loadUsers(headers, urlArr, dataArr => {
			data2Excel(dataArr, httpRes)
		})
	} else {
		const url = `https://ehire.51job.com/InboxResume/InboxViewEngine.aspx?JobID=${id}&tp=0&h=N`
		axios
			.get(url, {
				headers
			})
			.then(res => {
				const html = res.data
				let start = html.indexOf('<tbody>') + 7
				let end = html.indexOf('</tbody>')
				let data = html.substring(start, end)
				const urlArr = data.match(/hidSeqID\=(\S*)\&hidFolder/g)
				if (urlArr && urlArr.length > 0) {
					loadUsers(headers, urlArr, dataArr => {
						data2Excel(dataArr, httpRes)
					})
				} else {
					resSend(httpRes, 'cookie已经过期了，请重新复制', 0)
				}
			})
			.catch(err => {
				console.log(err)
				resSend(httpRes, err, 0)
			})
	}
})

function loadAllUsers(headers, listArr, successCallback) {
	let index = 0
	load1User()
	function load1User() {
		axios
			.get(listArr[index].link, {
				headers
			})
			.then(res => {
				let data = getValueByUserHtml(res.data)
				Object.assign(listArr[index], data)
				index++
			})
			.catch(err => {
				index++
			})
			.then(() => {
				console.log(`已完成爬取${index}条数据`)
				if (index === listArr.length) {
					successCallback(listArr)
				} else {
					load1User()
				}
			})
	}
}

function getValueByUserHtml(userHtml) {
	const statusMap = {
		1: '合适',
		2: '不合适',
		3: '待定',
		4: '已安排面试'
	}
	let positionReg = /hidPositionName\" value\=\"(\S*)\"/
	// let phoneReg = /phone_icon.png\"\>(\S*)\</;
	// let emailReg = /mailto\:(\S*)\"/;
	let statusReg = /hidQuickFeedback\" value\=\"(\S*)\" \/\>/
	let tagReg = /hidStrLabel\" value\=\"(\S*)\" \/\>/
	let companyOwnerReg =
		/p5\"\>\|\<\/span\>(\S*)\<\/td\>\<\/tr\>\<tr\>\<td valign\=\"top\" class\=\"time/
	let position = getMatch(positionReg)
	let phone = getValueByCut('phone_icon.png">', 11)
	let email = getValueByStartAndEnd('mailto:', '">')
	let zhuanYe = getValueFromStart('专　业：')
	let school = getValueFromStart('学　校：')
	let nowPosition = getValueFromStart('职　位：')
	let nowCompany = getValueFromStart('公　司：')
	let nowCompanyType = getValueFromStart('行　业：')
	let status = statusMap[getMatch(statusReg)] || ''
	let tag = getMatch(tagReg)
	let certificate = getCValue()
	let companyOwner = getMatch(companyOwnerReg)
	function getMatch(reg) {
		let matchValue = userHtml.match(reg)
		if (matchValue) {
			return matchValue[1]
		} else {
			return ''
		}
	}
	function getValueByCut(startKey, length) {
		let cutString = userHtml.split(startKey)[1]
		if (cutString) {
			return cutString.substr(0, length)
		} else {
			return ''
		}
	}
	function getValueByStartAndEnd(startKey, endKey) {
		let cutString = userHtml.split(startKey)[1]
		if (cutString) {
			let end = cutString.indexOf(endKey)
			return cutString.substr(0, end)
		} else {
			return ''
		}
	}
	function getValueFromStart(key) {
		const addStr = '</td><td valign="top" class="txt2">'
		let start = userHtml.indexOf(key)
		let value = userHtml.substr(start + key.length + addStr.length, 30)
		let end = value.indexOf('<')
		return value.substr(0, end)
	}
	function getCValue() {
		const cReg =
			/rtbox\"\>\<strong class\=\"txt3\"\>(\S*)\<\/strong\>\<\/td\>\<\/tr\>\<\/table\>\<\/td\>\<\/tr\>/g
		let cArr = userHtml.match(cReg)
		if (cArr) {
			return cArr
				.map(item => {
					let value = item.match(/txt3\"\>(\S*)\<\/strong/)[1]
					return value
				})
				.join('、')
		} else {
			return ''
		}
	}
	return {
		position,
		phone,
		email,
		zhuanYe,
		school,
		nowPosition,
		nowCompany,
		nowCompanyType,
		status,
		tag,
		certificate,
		companyOwner
	}
}

function loadUsers(headers, urlArr, successCallback) {
	const dataArr = []
	let index = 0
	load1User()
	const statusMap = {
		1: '合适',
		2: '不合适',
		3: '待定',
		4: '已安排面试'
	}
	function load1User() {
		const fullUrl = `https://ehire.51job.com/Candidate/ResumeViewFolderV2.aspx?${urlArr[index]}=EMP&hidFilter=0&pageCode=25`
		axios
			.get(fullUrl, {
				headers
			})
			.then(res => {
				let userHtml = res.data
				let nameReg = /resume-usernamehidden\" title\=\"(\S*)\"\>/
				let sendTimeReg = /hidPutDate\" value\=\"(\S*)\"/
				let positionReg = /hidPositionName\" value\=\"(\S*)\"/
				let genderReg = /gender.png\"\>(\S*)\</
				let ageReg = /info_part1\"\>\<ul\>\<li\>(\S*)岁/
				let phoneReg = /phone_icon.png\"\>(\S*)\</
				let emailReg = /mailto\:(\S*)\"/
				let workAgeReg = /li\>(\S*)年工作经验/
				// let xueLiReg = /学历\/学位：\<\/td\>\<td valign\=\"top" class\=\"txt2\"\>(\S*)\</;
				// let zhuanYeReg = /专　业：\<\/td\>\<td valign\=\"top\" class\=\"txt2\"\>(\S*)\</;
				// let schoolReg = /学　校：\<\/td\>\<td valign\=\"top\" class\=\"txt2\"\>(\S*)\</;
				// let nowPositionReg = /职　位：\<\/td\>\<td valign\=\"top\" class\=\"txt2\"\>(\S*)\</;
				// let nowCompanyReg = /公　司：\<\/td\>\<td valign\=\"top\" class\=\"txt2\"\>(\S*)\</;
				// let nowCompanyTypeReg = /行　业：\<\/td\>\<td valign\=\"top\" class\=\"txt2\"\>(\S*)\</;
				let statusReg = /hidQuickFeedback\" value\=\"(\S*)\" \/\>/
				let tagReg = /hidStrLabel\" value\=\"(\S*)\" \/\>/
				let companyOwnerReg =
					/p5\"\>\|\<\/span\>(\S*)\<\/td\>\<\/tr\>\<tr\>\<td valign\=\"top\" class\=\"time/
				index++
				dataArr.push({
					name: getMatch(nameReg),
					sendTime: getMatch(sendTimeReg),
					position: getMatch(positionReg),
					gender: getMatch(genderReg),
					phone: getMatch(phoneReg),
					email: getMatch(emailReg),
					workAge: getMatch(workAgeReg) || '0',
					age: getMatch(ageReg),
					xueLi: getValueFromStart('学历/学位：'),
					birthday: resetBirthday(userHtml.substr(userHtml.indexOf('岁（') + 2, 11)),
					zhuanYe: getValueFromStart('专　业：'),
					school: getValueFromStart('学　校：'),
					nowPosition: getValueFromStart('职　位：'),
					nowCompany: getValueFromStart('公　司：'),
					nowCompanyType: getValueFromStart('行　业：'),
					status: statusMap[getMatch(statusReg)] || '',
					tag: getMatch(tagReg),
					certificate: getCValue(),
					companyOwner: getMatch(companyOwnerReg)
				})
				if (urlArr[index]) {
					load1User()
				} else {
					console.log(dataArr)
					successCallback(dataArr)
				}
				function getMatch(reg) {
					let matchValue = userHtml.match(reg)
					if (matchValue) {
						return matchValue[1]
					} else {
						return ''
					}
				}

				function getValueFromStart(key) {
					const addStr = '</td><td valign="top" class="txt2">'
					let start = userHtml.indexOf(key)
					let value = userHtml.substr(start + key.length + addStr.length, 30)
					let end = value.indexOf('<')
					return value.substr(0, end)
				}

				function resetBirthday(value) {
					let end = value.indexOf('日')
					return value.substr(0, end + 1)
				}

				function getCValue() {
					const cReg =
						/rtbox\"\>\<strong class\=\"txt3\"\>(\S*)\<\/strong\>\<\/td\>\<\/tr\>\<\/table\>\<\/td\>\<\/tr\>/g
					let cArr = userHtml.match(cReg)
					if (cArr) {
						return cArr
							.map(item => {
								let value = item.match(/txt3\"\>(\S*)\<\/strong/)[1]
								return value
							})
							.join('、')
					} else {
						return ''
					}
				}
			})
	}
}

function data2Excel(dataArr, res) {
	const colsArr = [
		{ caption: '投递时间', type: 'string', key: 'sendTime' },
		{ caption: '投递岗位', type: 'string', key: 'position' },
		{ caption: '名字', type: 'string', key: 'name' },
		// { caption: '出生日期', type: 'string', key: 'birthday' },
		{ caption: '年龄', type: 'string', key: 'age' },
		{ caption: '手机', type: 'string', key: 'phone' },
		{ caption: '邮箱', type: 'string', key: 'email' },
		{ caption: '工作年限', type: 'string', key: 'workAge' },
		{ caption: '性别', type: 'string', key: 'gender' },
		{ caption: '职能', type: 'string', key: 'jobAbility' },
		{ caption: '学历', type: 'string', key: 'xueLi' },
		{ caption: '专业', type: 'string', key: 'zhuanYe' },
		{ caption: '学校', type: 'string', key: 'school' },
		{ caption: '目前职位', type: 'string', key: 'nowPosition' },
		{ caption: '最近一份工作就职单位名称', type: 'string', key: 'nowCompany' },
		{ caption: '现就职单位性质', type: 'string', key: 'companyOwner' },
		{ caption: '职称（证书）名称', type: 'string', key: 'certificate' },
		{ caption: '简历标签', type: 'string', key: 'tag' },
		{ caption: '现就职单位行业', type: 'string', key: 'nowCompanyType' }
	]
	let conf = {
		name: 'sheet1',
		cols: colsArr,
		rows: dataArr.map(user => {
			return colsArr.map(item => {
				return user[item.key]
			})
		})
	}
	let result = nodeExcel.execute(conf)
	res.setHeader('Content-Type', 'application/vnd.openxmlformats;charset=utf-8')
	res.setHeader('Content-Disposition', 'attachment; filename=jianli.xlsx')
	res.end(result, 'binary')
}

module.exports = router
